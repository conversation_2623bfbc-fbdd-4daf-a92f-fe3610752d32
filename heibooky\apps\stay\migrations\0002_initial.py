# Generated by Django 5.1.2 on 2025-06-28 07:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('stay', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='staffs',
            field=models.ManyToManyField(related_name='properties', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photo',
            name='property',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='stay.property'),
        ),
        migrations.AddField(
            model_name='guestarrivalinfo',
            name='property',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='guest_arrival_info', to='stay.property'),
        ),
        migrations.AddField(
            model_name='propertyamenity',
            name='amenity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.amenity'),
        ),
        migrations.AddField(
            model_name='propertyamenity',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.property'),
        ),
        migrations.AddField(
            model_name='amenity',
            name='properties',
            field=models.ManyToManyField(related_name='amenities', through='stay.PropertyAmenity', to='stay.property'),
        ),
        migrations.AddField(
            model_name='propertymetadata',
            name='property',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='metadata', to='stay.property'),
        ),
        migrations.AddField(
            model_name='propertyownership',
            name='property',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ownership', to='stay.property'),
        ),
        migrations.AddField(
            model_name='propertyownership',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddConstraint(
            model_name='propertypermission',
            constraint=models.UniqueConstraint(fields=('name',), name='unique_property_permission'),
        ),
        migrations.AddField(
            model_name='room',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rooms', to='stay.property'),
        ),
        migrations.AddField(
            model_name='photo',
            name='room',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='stay.room'),
        ),
        migrations.AddField(
            model_name='roomamenity',
            name='amenity',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.amenity'),
        ),
        migrations.AddField(
            model_name='roomamenity',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='amenities', to='stay.room'),
        ),
        migrations.AddField(
            model_name='staffrole',
            name='permissions',
            field=models.ManyToManyField(related_name='staff_roles', to='stay.propertypermission'),
        ),
        migrations.AddField(
            model_name='staffrole',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_roles', to='stay.property'),
        ),
        migrations.AddField(
            model_name='staffrole',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='property_roles', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='teaminvite',
            name='invited_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='teaminvite',
            name='permissions',
            field=models.ManyToManyField(related_name='invites', to='stay.propertypermission'),
        ),
        migrations.AddField(
            model_name='teaminvite',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invites', to='stay.property'),
        ),
        migrations.AddField(
            model_name='teaminvite',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='team_invites', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='propertyamenity',
            unique_together={('property', 'amenity')},
        ),
        migrations.AddIndex(
            model_name='amenity',
            index=models.Index(fields=['name', 'category'], name='stay_amenit_name_7423f0_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='amenity',
            unique_together={('name', 'category')},
        ),
        migrations.AlterUniqueTogether(
            name='staffrole',
            unique_together={('property', 'user')},
        ),
        migrations.AddIndex(
            model_name='teaminvite',
            index=models.Index(fields=['email', 'accepted'], name='stay_teamin_email_158a85_idx'),
        ),
        migrations.AddIndex(
            model_name='teaminvite',
            index=models.Index(fields=['expires_at'], name='stay_teamin_expires_1043a4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='teaminvite',
            unique_together={('property', 'email')},
        ),
    ]
