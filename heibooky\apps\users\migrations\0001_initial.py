# Generated by Django 5.1.2 on 2025-06-28 07:58

import apps.users.models
import django.core.validators
import django.db.models.deletion
import phonenumber_field.modelfields
import services.storage.storage
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.Email<PERSON>ield(error_messages={'unique': 'A user with that email already exists.'}, max_length=255, unique=True, verbose_name='Email Address')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255, verbose_name='Full Name')),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, help_text='Used as an alternative verification method', max_length=128, null=True, region=None, verbose_name='Phone Number')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_verified', models.BooleanField(default=False, help_text='Designates whether this user has completed verification.', verbose_name='Verified')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active.', verbose_name='Active')),
                ('has_set_password', models.BooleanField(default=False, help_text='Indicates if the user has set a password.', verbose_name='Password Set')),
                ('is_admin', models.BooleanField(default=False, verbose_name='Admin Status')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='Staff Status')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='custom_user_permissions_set', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, storage=services.storage.storage.PhotoStorage(bucket_name='heibooky', custom_domain='cdn.heibooky.com', default_acl='public-read', location='media', object_parameters={'CacheControl': 'max-age=86400', 'ContentDisposition': 'inline', 'ContentType': 'image/jpeg', 'Metadata': {'uploaded-by': 'heibooky-backend'}}), upload_to='profile_images/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif']), apps.users.models.validate_image_size], verbose_name='Profile Image')),
                ('last_login_location', models.JSONField(blank=True, null=True)),
                ('has_billing_profile', models.BooleanField(default=False, verbose_name='Has Billing Profile')),
                ('is_customer', models.BooleanField(default=False, verbose_name='Is Customer')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
            },
        ),
    ]
