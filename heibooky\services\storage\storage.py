import logging
import os
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from urllib.parse import urljoin

from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from storages.backends.s3boto3 import S3Boto3Storage
from botocore.exceptions import ClientError, BotoCoreError
from PIL import Image, ImageOps
import io

logger = logging.getLogger(__name__)


class OptimizedCDNStorage(S3Boto3Storage):
    """
    Custom S3 storage backend that ensures all files are served via CDN
    with proper cache headers and optimization.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure CDN URL is used for all file URLs
        self.custom_domain = getattr(settings, 'AWS_CDN_URL', '').replace('https://', '').replace('http://', '')
        if self.custom_domain:
            self.url_protocol = 'https:'
    
    def url(self, name):
        """Override to ensure CDN URLs are always returned"""
        try:
            if self.custom_domain:
                # Construct CDN URL directly
                cdn_url = f"https://{self.custom_domain}/{settings.AWS_LOCATION}/{name}"
                logger.debug(f"Generated CDN URL: {cdn_url}")
                return cdn_url
            else:
                # Fallback to default S3 URL
                return super().url(name)
        except Exception as e:
            logger.error(f"Error generating CDN URL for {name}: {str(e)}")
            return super().url(name)
    
    def _save(self, name, content):
        """Enhanced save method with error handling and logging"""
        try:
            logger.info(f"Uploading file: {name}, size: {content.size if hasattr(content, 'size') else 'unknown'}")
            result = super()._save(name, content)
            logger.info(f"Successfully uploaded file: {result}")
            return result
        except ClientError as e:
            logger.error(f"AWS S3 error uploading {name}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error uploading {name}: {str(e)}")
            raise


class PhotoStorage(OptimizedCDNStorage):
    """
    Custom storage for photos with image optimization and proper content-type handling
    """
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('object_parameters', {})
        kwargs['object_parameters'].update({
            'CacheControl': 'max-age=86400',
            'ContentType': 'image/jpeg',
        })
        super().__init__(*args, **kwargs)
    
    def _save(self, name, content):
        """Save with image optimization"""
        try:
            # Optimize image before saving
            optimized_content = self._optimize_image(content, name)
            return super()._save(name, optimized_content)
        except Exception as e:
            logger.error(f"Error optimizing image {name}: {str(e)}")
            # Fallback to original content if optimization fails
            return super()._save(name, content)
    
    def _optimize_image(self, content, name: str) -> ContentFile:
        """
        Optimize image by resizing and compressing while maintaining quality
        """
        try:
            # Reset content position
            if hasattr(content, 'seek'):
                content.seek(0)
            
            # Open image with PIL
            image = Image.open(content)
            
            # Convert to RGB if necessary (for PNG with transparency)
            if image.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparent images
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Auto-orient image based on EXIF data
            image = ImageOps.exif_transpose(image)
            
            # Calculate optimal size (max 1920x1920 while maintaining aspect ratio)
            max_size = (1920, 1920)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Optimize and save
            output = io.BytesIO()
            
            # Determine quality based on file size
            quality = 85
            if image.size[0] * image.size[1] > 1000000:  # > 1MP
                quality = 80
            
            image.save(output, format='JPEG', quality=quality, optimize=True)
            output.seek(0)
            
            # Update filename to .jpg if it wasn't already
            if not name.lower().endswith(('.jpg', '.jpeg')):
                name_parts = name.rsplit('.', 1)
                name = f"{name_parts[0]}.jpg"
            
            optimized_content = ContentFile(output.getvalue(), name=name)
            
            logger.info(f"Optimized image {name}: original size unknown, new size: {len(output.getvalue())} bytes")
            
            return optimized_content
            
        except Exception as e:
            logger.error(f"Failed to optimize image {name}: {str(e)}")
            # Return original content if optimization fails
            if hasattr(content, 'seek'):
                content.seek(0)
            return content


class DocumentStorage(OptimizedCDNStorage):
    """
    Custom storage for documents (PDFs, etc.) with appropriate content-type and caching
    """
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('object_parameters', {})
        kwargs['object_parameters'].update({
            'CacheControl': 'max-age=86400',
            'ContentType': 'application/pdf',
        })
        super().__init__(*args, **kwargs)
    
    def _save(self, name, content):
        """Save with document-specific handling"""
        try:
            # Determine content type based on file extension
            file_ext = os.path.splitext(name)[1].lower()
            content_type_map = {
                '.pdf': 'application/pdf',
                '.doc': 'application/msword',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                '.txt': 'text/plain',
            }
            
            content_type = content_type_map.get(file_ext, 'application/octet-stream')
            
            # Update object parameters for this specific file
            self.object_parameters = self.object_parameters.copy()
            self.object_parameters['ContentType'] = content_type
            
            logger.info(f"Uploading document {name} with content-type: {content_type}")
            
            return super()._save(name, content)
        except Exception as e:
            logger.error(f"Error uploading document {name}: {str(e)}")
            raise


class CDNStorageService:
    """
    Service class to handle CDN storage operations with fallback and error handling
    """
    
    @staticmethod
    def get_storage_backend(storage_type: str = 'default'):
        """
        Get the appropriate storage backend
        """
        storage_map = {
            'photo': PhotoStorage,
            'document': DocumentStorage,
            'default': OptimizedCDNStorage,
        }
        
        StorageClass = storage_map.get(storage_type, OptimizedCDNStorage)
        return StorageClass()
    
    @staticmethod
    def upload_file(file_content, filename: str, storage_type: str = 'default') -> Optional[str]:
        """
        Upload file to CDN storage with error handling
        
        Args:
            file_content: File content to upload
            filename: Name of the file
            storage_type: Type of storage ('photo', 'document', 'default')
            
        Returns:
            CDN URL of uploaded file or None if failed
        """
        try:
            storage = CDNStorageService.get_storage_backend(storage_type)
            saved_name = storage.save(filename, file_content)
            url = storage.url(saved_name)
            
            logger.info(f"Successfully uploaded {filename} to CDN: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Failed to upload {filename} to CDN: {str(e)}")
            return None

    @staticmethod
    def upload_file_with_path(file_content, filename: str, storage_type: str = 'default') -> Optional[Tuple[str, str]]:
        """
        Upload file to CDN storage and return both URL and relative path
        
        Args:
            file_content: File content to upload
            filename: Name of the file
            storage_type: Type of storage ('photo', 'document', 'default')
            
        Returns:
            Tuple of (CDN URL, relative path) or None if failed
        """
        try:
            storage = CDNStorageService.get_storage_backend(storage_type)
            saved_name = storage.save(filename, file_content)
            url = storage.url(saved_name)
            
            logger.info(f"Successfully uploaded {filename} to CDN: {url}, path: {saved_name}")
            return url, saved_name
            
        except Exception as e:
            logger.error(f"Failed to upload {filename} to CDN: {str(e)}")
            return None

    @staticmethod
    def get_relative_path_from_url(url: str, storage_type: str = 'default') -> Optional[str]:
        """
        Extract the relative storage path from a CDN URL
        
        Args:
            url: Full CDN URL
            storage_type: Type of storage used
            
        Returns:
            Relative path that can be stored in database, or None if extraction fails
        """
        try:
            storage = CDNStorageService.get_storage_backend(storage_type)
            
            # For S3-based storage, remove the domain and location prefix
            if hasattr(storage, 'custom_domain') and storage.custom_domain:
                # Remove CDN domain
                if url.startswith(f"https://{storage.custom_domain}/"):
                    path = url.replace(f"https://{storage.custom_domain}/", '')
                elif url.startswith(f"http://{storage.custom_domain}/"):
                    path = url.replace(f"http://{storage.custom_domain}/", '')
                else:
                    # Try to extract from any domain
                    from urllib.parse import urlparse
                    parsed = urlparse(url)
                    path = parsed.path.lstrip('/')
                
                # Remove location prefix if present
                if hasattr(settings, 'AWS_LOCATION') and settings.AWS_LOCATION:
                    location_prefix = f"{settings.AWS_LOCATION}/"
                    if path.startswith(location_prefix):
                        path = path[len(location_prefix):]
                
                return path
            
            # Fallback: try to extract from URL
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path = parsed.path.lstrip('/')
            
            # Remove AWS_LOCATION prefix if present
            if hasattr(settings, 'AWS_LOCATION') and settings.AWS_LOCATION:
                location_prefix = f"{settings.AWS_LOCATION}/"
                if path.startswith(location_prefix):
                    path = path[len(location_prefix):]
            
            return path
            
        except Exception as e:
            logger.error(f"Failed to extract relative path from URL {url}: {str(e)}")
            return None
    
    @staticmethod
    def delete_file(filename: str, storage_type: str = 'default') -> bool:
        """
        Delete file from CDN storage
        
        Args:
            filename: Name of the file to delete
            storage_type: Type of storage
            
        Returns:
            True if successful, False otherwise
        """
        try:
            storage = CDNStorageService.get_storage_backend(storage_type)
            storage.delete(filename)
            
            logger.info(f"Successfully deleted {filename} from CDN")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete {filename} from CDN: {str(e)}")
            return False
    
    @staticmethod
    def get_file_url(filename: str, storage_type: str = 'default') -> Optional[str]:
        """
        Get CDN URL for a file
        
        Args:
            filename: Name of the file
            storage_type: Type of storage
            
        Returns:
            CDN URL or None if not found
        """
        try:
            storage = CDNStorageService.get_storage_backend(storage_type)
            if storage.exists(filename):
                return storage.url(filename)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get URL for {filename}: {str(e)}")
            return None
    
    @staticmethod
    def check_cdn_health() -> Dict[str, Any]:
        """
        Check CDN and storage health
        
        Returns:
            Dictionary with health status information
        """
        health_info = {
            'cdn_available': False,
            'storage_available': False,
            'errors': []
        }
        
        try:
            # Test storage connection
            storage = CDNStorageService.get_storage_backend('default')
            
            # Try to list objects (with minimal request)
            test_file = ContentFile(b'test', name='health_check.txt')
            test_name = storage.save('health_check.txt', test_file)
            
            # Clean up test file
            storage.delete(test_name)
            
            health_info['storage_available'] = True
            health_info['cdn_available'] = True
            
        except ClientError as e:
            error_msg = f"AWS S3 error: {str(e)}"
            health_info['errors'].append(error_msg)
            logger.error(error_msg)
            
        except Exception as e:
            error_msg = f"Storage health check failed: {str(e)}"
            health_info['errors'].append(error_msg)
            logger.error(error_msg)
        
        return health_info
