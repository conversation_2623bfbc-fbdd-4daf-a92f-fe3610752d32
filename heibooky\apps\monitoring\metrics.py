import time
import logging
import psutil
from django.conf import settings
from django.core.cache import cache
from django.db import connections
from django.db.utils import OperationalError
from prometheus_client import Counter, Histogram, Gauge, Info, generate_latest, CONTENT_TYPE_LATEST
from celery import current_app as celery_app

logger = logging.getLogger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter(
    'django_http_requests_total',
    'Total HTTP requests',
    ['method', 'status', 'endpoint']
)

REQUEST_LATENCY = Histogram(
    'django_http_request_duration_seconds',
    'HTTP request latency',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'django_http_requests_active',
    'Active HTTP requests'
)

DATABASE_CONNECTIONS = Gauge(
    'django_database_connections_total',
    'Total database connections',
    ['database']
)

CACHE_OPERATIONS = Counter(
    'django_cache_operations_total',
    'Total cache operations',
    ['operation', 'result']
)

CELERY_TASKS = Counter(
    'celery_tasks_total',
    'Total Celery tasks',
    ['task_name', 'state']
)

CELERY_QUEUE_LENGTH = Gauge(
    'celery_queue_length',
    'Celery queue length',
    ['queue']
)

CELERY_WORKERS = Gauge(
    'celery_workers_total',
    'Total Celery workers'
)

PROCESS_MEMORY = Gauge(
    'django_process_memory_usage_bytes',
    'Process memory usage in bytes'
)

PROCESS_CPU = Gauge(
    'django_process_cpu_percent',
    'Process CPU usage percentage'
)

APP_INFO = Info(
    'django_app_info',
    'Application information'
)


class MetricsCollector:
    """Collects various application metrics."""
    
    def __init__(self):
        self._process = None

    @property
    def process(self):
        if self._process is None:
            self._process = psutil.Process()
        return self._process    
    def collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            # Memory usage
            memory_info = self.process.memory_info()
            PROCESS_MEMORY.set(memory_info.rss)
            
            # CPU usage
            # CPU percent needs interval or two calls to get accurate results
            cpu_percent = self.process.cpu_percent(interval=0.1)
            PROCESS_CPU.set(cpu_percent)            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def collect_database_metrics(self):
        """Collect database connection metrics."""
        try:
            for db_name, db_config in settings.DATABASES.items():
                try:
                    conn = connections[db_name]
                    # Get actual connection count from database
                    with conn.cursor() as cursor:
                        if db_config['ENGINE'] == 'django.db.backends.postgresql':
                            cursor.execute(
                                "SELECT count(*) FROM pg_stat_activity WHERE datname = %s",
                                [db_config['NAME']]
                            )
                            count = cursor.fetchone()[0]
                            DATABASE_CONNECTIONS.labels(database=db_name).set(count)                
                except OperationalError:
                    DATABASE_CONNECTIONS.labels(database=db_name).set(0)
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
    
    def collect_cache_metrics(self):
        """Test cache connectivity and collect metrics."""
        try:
            # Test cache connectivity
            start_time = time.time()
            test_key = 'metrics_test'
            cache.set(test_key, 'test_value', timeout=1)
            result = cache.get(test_key)
            cache.delete(test_key)
            
            if result == 'test_value':
                CACHE_OPERATIONS.labels(operation='test', result='success').inc()
            else:
                CACHE_OPERATIONS.labels(operation='test', result='failure').inc()
                
        except Exception as e:
            logger.error(f"Error collecting cache metrics: {e}")
            CACHE_OPERATIONS.labels(operation='test', result='error').inc()
            # Add timeout to prevent hanging
            inspect = celery_app.control.inspect(timeout=5.0)
            
            # Active workers
            active_workers = inspect.active()
            if active_workers:
                CELERY_WORKERS.set(len(active_workers))
            else:
                CELERY_WORKERS.set(0)
            
            # Queue lengths
            if hasattr(celery_app.control, 'inspect'):
                reserved = inspect.reserved()
                if reserved:
                    for worker, tasks in reserved.items():
                        # Extract queue name from worker name or use default
                        queue_name = worker.split('@')[0] if '@' in worker else 'default'
                        CELERY_QUEUE_LENGTH.labels(queue=queue_name).set(len(tasks))
                        
        except Exception as e:
            logger.error(f"Error collecting Celery metrics: {e}")
    
    def collect_all_metrics(self):
        """Collect all metrics."""
        self.collect_system_metrics()
        self.collect_database_metrics()
        self.collect_cache_metrics()
        self.collect_celery_metrics()


# Global metrics collector instance
metrics_collector = MetricsCollector()


def get_health_status():
    """Get application health status."""
    health = {
        'status': 'healthy',
        'timestamp': time.time(),
        'checks': {}
    }
    
    # Database check
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        health['checks']['database'] = 'healthy'
    except Exception as e:
        health['checks']['database'] = f'unhealthy: {str(e)}'
        health['status'] = 'unhealthy'
    
    # Cache check
    try:
        cache.set('health_check', 'ok', timeout=1)
        result = cache.get('health_check')
        if result == 'ok':
            health['checks']['cache'] = 'healthy'
        else:
            health['checks']['cache'] = 'unhealthy: cache test failed'
            health['status'] = 'unhealthy'
        cache.delete('health_check')
    except Exception as e:
        health['checks']['cache'] = f'unhealthy: {str(e)}'
        health['status'] = 'unhealthy'
    
    # Celery check
    try:
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        if active_workers:
            health['checks']['celery'] = 'healthy'
        else:
            health['checks']['celery'] = 'unhealthy: no active workers'
            health['status'] = 'degraded'
    except Exception as e:
        health['checks']['celery'] = f'unhealthy: {str(e)}'
        health['status'] = 'degraded'
    
    return health
