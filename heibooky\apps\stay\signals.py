from django.db.models.signals import post_save, pre_save, pre_delete, post_delete, m2m_changed
from django.dispatch import receiver
from contextlib import contextmanager
from threading import local
from apps.stay.models import Room, RoomAmenity, Property, Photo, TeamInvite, GuestArrivalInfo, PropertyMetadata
from apps.integrations.tasks import handle_rooms, handle_property, handle_photos
from apps.booking.tasks.inventory import set_room_inventory_for_year, sync_inventory_for_empty_dates
from services.su_api import onboard_rooms, delete_property
from apps.integrations.utils import build_room_update_data, log_action
from services.email import PropertyEmailService
import logging
from services.notification import GeneralNotificationHandler
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext as _
from apps.stay.utils.property_setup import update_property_setup_status

logger = logging.getLogger(__name__)

# Thread-local storage to track property deletion context
_thread_locals = local()

@contextmanager
def property_deletion_context():
    """
    Context manager to track when a room is being deleted as part of a property deletion.
    This prevents unnecessary API calls for individual room deletions when the entire
    property is being deleted.
    """
    _thread_locals.deleting_property = True
    try:
        yield
    finally:
        _thread_locals.deleting_property = False

def is_property_being_deleted():
    """
    Check if we're currently in a property deletion context.
    """
    return getattr(_thread_locals, 'deleting_property', False)


@receiver(pre_save, sender=Room)
def cache_original_room_fields(sender, instance, **kwargs):
    """
    Cache the original 'is_active', 'is_onboarded', and 'room_rate' fields and track changed fields for the Room model.
    This helps detect changes in these critical fields and track other modified fields.
    """
    if instance.pk:
        try:
            original = Room.objects.get(pk=instance.pk)
            instance._original_is_active = original.is_active
            instance._original_is_onboarded = original.is_onboarded
            instance._original_room_rate = original.room_rate
            # Track fields that have changed
            instance._changed_fields = [
                field.name for field in instance._meta.fields
                if getattr(original, field.name) != getattr(instance, field.name)
            ]
        except Room.DoesNotExist:
            instance._changed_fields = []
    else:
        instance._changed_fields = []

@receiver(pre_save, sender=Property)
def set_property_ids(sender, instance, **kwargs):
    """
    Signal to set hotel_id for new Property instances.
    The hotel_id is set using the first 8 characters of the property's UUID.
    """
    if not instance.hotel_id and instance.id:  # Check if id exists and hotel_id isn't set
        instance.hotel_id = str(instance.id)[:8]

@receiver(m2m_changed, sender=Property.staffs.through)
def set_chain_id(sender, instance, action, pk_set, **kwargs):
    """
    Signal to set chain_id when staff members are added to a Property.
    The chain_id is set using the last 8 characters of the first staff member's ID.
    """
    if action == "post_add" and pk_set and not instance.chain_id:

        User = get_user_model()
        staff = User.objects.filter(id__in=pk_set).first()
        if staff:
            instance.chain_id = str(staff.id)[-8:]
            instance.save()

@receiver(post_save, sender=Room)
@receiver(post_save, sender=RoomAmenity)
def room_update_handler(sender, instance, created, **kwargs):
    """
    Handle room updates and send appropriate update to the SU API.
    Logs actions to the SUAPIActionLog model.
    """
    if sender == RoomAmenity:
        room_instance = instance.room
        user = room_instance.property.staffs.first()
        if user:
            handle_rooms.delay(user.id, "update", room_id=room_instance.id, update_type="amenities_update")
        return

    room_instance = instance
    if not room_instance.property.is_onboarded:
        return

    user = room_instance.property.staffs.first()
    if not user:
        return

    action = "update" if not created else "create"

    if created and sender == Room:
        handle_rooms.delay(user.id, action, property_id=instance.property.id)
        return

    # Handle is_active status change separately if detected
    if hasattr(instance, '_original_is_active') and instance._original_is_active != instance.is_active:
        handle_rooms.delay(user.id, action, room_id=instance.id, update_type="is_active_update")    # Handle is_onboarded status change - trigger inventory setup when room becomes onboarded
    if (hasattr(instance, '_original_is_onboarded') and 
        instance._original_is_onboarded is False and 
        instance.is_onboarded is True):
        # Room has just been onboarded - set up inventory for one year
        logger.info(f"Room {instance.id} onboarding status changed from False to True - triggering inventory setup")
        from django.db import transaction
        transaction.on_commit(lambda: set_room_inventory_for_year.delay(str(instance.id)))
        logger.info(f"Triggered one-year inventory setup for newly onboarded room {instance.id}")

    # Handle room_rate change - trigger inventory sync for empty dates
    if (hasattr(instance, '_original_room_rate') and 
        instance._original_room_rate != instance.room_rate):
        # Room rate has changed - sync inventory for empty dates
        logger.info(f"Room {instance.id} rate changed from {instance._original_room_rate} to {instance.room_rate} - triggering inventory sync")
        from django.db import transaction
        transaction.on_commit(lambda: sync_inventory_for_empty_dates.delay(str(instance.property.id)))
        logger.info(f"Triggered inventory sync for property {instance.property.id} due to room rate change")

    # General update for other fields
    if hasattr(instance, '_changed_fields') and any(field for field in instance._changed_fields if field not in {'is_active', 'is_onboarded', 'room_rate'}):
        handle_rooms.delay(user.id, action, room_id=instance.id, update_type="general_update")

@receiver(pre_delete, sender=Room)
def room_delete_handler(sender, instance, **kwargs):
    """
    Sends a delete request to SU API for the specific room before it is deleted from the database.
    Skip if the room is being deleted as part of a property deletion.
    """
    # Skip if this is part of a property deletion
    if is_property_being_deleted() or not instance.property.is_onboarded or not instance.is_onboarded:
        return

    user = instance.property.staffs.first()
    try:
        data = build_room_update_data(instance, update_type="room_delete")
        response = onboard_rooms(data)

        log_action(
            user=user,
            property_id=instance.property.id,
            action="delete",
            description="Room delete request sent to SU API",
            status="successful" if response.get("Status") == "Success" else "failed",
            details={"response": response}
        )

    except Exception as e:
        log_action(
            user=user,
            property_id=instance.property.id,
            action="delete",
            description="Error during room delete",
            status="failed",
            details={"error": str(e)}
        )

class PropertyDeletionHandler:
    """Handles the property deletion process including API calls, notifications, and emails"""

    def __init__(self, property_instance):
        self.property = property_instance
        self.users = property_instance.staffs.all()
        self.first_user = self.users.first() if self.users.exists() else None
        self.email_service = PropertyEmailService()
        

    def _send_su_api_delete_request(self):
        """Sends delete request to SU API and logs the action"""
        if not self.property.is_onboarded:
            return None

        with property_deletion_context():
            response = delete_property(self.property.hotel_id)

            # Log the API action
            log_action(
                user=self.first_user,
                property_id=self.property.id,
                action="delete",
                description="Property delete request sent to SU API",
                status="successful" if response.get("Status") == "Success" else "failed",
                details={"response": response}
            )

            return response

    def _create_notification(self, user):
        """Creates and sends notification for property deletion"""
        title = "Eliminazione Proprietà Completata"
        message = (
            f"La richiesta di eliminazione per la proprietà '{self.property.name}' "
            "è stata elaborata con successo. Tutti i dati relativi alla struttura "
            "sono stati rimossi dai nostri sistemi."
        )

        handler = GeneralNotificationHandler(
            users=user,
            title=title,
            message=message
        )
        handler.send_notification()

    def _notify_user(self, user):
        """Sends email confirmation and creates notifications for a user"""
        try:
            # Send deletion confirmation email using PropertyEmailService
            self.email_service.send_deletion_confirmation(
                property_obj=self.property,
                user=user
            )
            # Create and send notification
            self._create_notification(user)

        except Exception as e:
            logger.error(
                f"Failed to send deletion confirmation to {user.email}: {str(e)}",
                exc_info=True
            )

    def handle_deletion(self):
        """Main method to handle the complete property deletion process"""
        try:
            # Send API delete request if property is onboarded
            api_response = self._send_su_api_delete_request()

            # Notify all staff members
            for user in self.users:
                self._notify_user(user)

        except Exception as e:
            logger.error(
                f"Error during property deletion for property {self.property.id}: {str(e)}",
                exc_info=True
            )
            # Log the failed action
            log_action(
                user=self.first_user,
                property_id=self.property.id,
                action="delete",
                description="Error during property delete",
                status="failed",
                details={"error": str(e)}
            )
            raise  # Re-raise the exception to ensure the deletion is rolled back if needed

@receiver(pre_delete, sender=Property)
def property_delete_handler(sender, instance, **kwargs):
    """
    Signal handler for Property deletion.
    Handles API deletion request, staff notifications, and confirmation emails.
    """
    handler = PropertyDeletionHandler(instance)
    handler.handle_deletion()

@receiver(post_save, sender=Property)
@receiver(post_save, sender='stay.PropertyAmenity')
@receiver(post_save, sender='stay.PropertyMetadata')
@receiver(post_save, sender='stay.GuestArrivalInfo')
@receiver(post_save, sender='stay.Location')
def handle_property_updates(sender, instance, **kwargs):
    """
    Combined signal handler to update property data on SU API when:
    1. Property model is updated directly
    2. Related models (PropertyMetadata, GuestArrivalInfo, Location) are modified
    """
    # Get the property instance based on sender
    if sender == Property:
        property_instance = instance
        if property_instance.location:
            # Use a flag on the location object to prevent infinite recursion
            if not hasattr(property_instance.location, '_skip_update'):
                property_instance.location._skip_update = True
                property_instance.location.is_editable = not property_instance.is_onboarded
                property_instance.location.save(update_fields=['is_editable'])
                delattr(property_instance.location, '_skip_update')
        # Skip updates for specific fields
        if ('update_fields' in kwargs and kwargs['update_fields'] is not None and
            {'is_onboarded', 'is_multi_unit', 'is_active', 'cover_image'}.intersection(kwargs['update_fields'])):
            return
    elif sender._meta.model_name == 'location':
        # Skip if this is just an is_editable update from the Property signal
        if hasattr(instance, '_skip_update') or ('update_fields' in kwargs and kwargs['update_fields'] == ['is_editable']):
            return
        property_instance = Property.objects.filter(location=instance).first()
    else:
        property_instance = instance.property

    # Skip if no property found or property is not onboarded
    if not property_instance or not property_instance.is_onboarded:
        return

    user = property_instance.staffs.first()
    if user:
        handle_property.delay(request_type="Overlay", user=user.id, action="update", property_id=property_instance.id)
    else:
        logger.error(f"No staff members found for property {property_instance.id} in handle_property_updates")



@receiver(post_save, sender=Photo)
@receiver(post_delete, sender=Photo)
def handle_property_photo_deployment(sender, instance, **kwargs):
    """
    Handle the deployment of property photos to SU API when:
    1. A new photo is created
    2. An existing photo is updated
    3. The property is already onboarded
    4. The photo is deleted

    Photos are processed in batches of 5, with image association happening after each successful batch upload.
    """
    try:
        # For delete operations, use cached property information
        if 'signal' in kwargs and kwargs['signal'] == post_delete:
            if not hasattr(instance, '_cached_property_id') or not instance._cached_property_id:
                return
            if not getattr(instance, '_cached_property_is_onboarded', False):
                return
            
            property_id = instance._cached_property_id
            
            # Get property instance for user lookup
            try:
                property_instance = Property.objects.get(id=property_id)
            except Property.DoesNotExist:
                # Property was deleted, no need to process
                return
        else:
            # For create/update operations, validate property exists and is onboarded
            try:
                if not instance.property or not instance.property.is_onboarded:
                    return
                property_instance = instance.property
            except Property.DoesNotExist:
                # Property was deleted, no need to process
                return

        # Validate user exists
        user = property_instance.staffs.first()
        if not user:
            logger.error(f"No staff members found for property {property_instance.id}")
            return

        # Determine if this is a create/update or delete action based on the signal
        if 'signal' in kwargs and kwargs['signal'] == post_delete:
            action = "delete"
        elif kwargs.get('created', False):
            action = "create"
        else:
            action = "update"

        # Call the task with the correct parameters
        logger.info(f"Triggering photo deployment for property {property_instance.id} with action: {action}")
        handle_photos.delay(property_instance.id, user.id, action)

    except Exception as e:
        logger.error(f"Error in handle_property_photo_deployment: {str(e)}", exc_info=True)


@receiver(post_save, sender=Photo)
def handle_property_cover_image_on_create(sender, instance, created, **kwargs):
    """
    Handle setting the property cover image when a new photo is created.

    This signal:
    1. Triggers an asynchronous task to process the cover image
    2. Only processes property photos (not room photos)
    3. Only runs when a new photo is created
    """
    try:
        # Only process for property photos and new photos
        if not instance.property or not created:
            return

        property_instance = instance.property

        # Trigger the asynchronous task to process the cover image
        from apps.stay.tasks import process_property_cover_image
        logger.info(f"Triggering cover image processing for property {property_instance.id} after photo creation")
        process_property_cover_image.delay(
            property_id=str(property_instance.id),
            photo_id=str(instance.id),
            action="create"
        )

    except Exception as e:
        logger.error(f"Error in handle_property_cover_image_on_create: {str(e)}", exc_info=True)


@receiver(pre_delete, sender=Photo)
def cache_photo_property(sender, instance, **kwargs):
    """
    Cache property information before photo deletion to avoid 
    DoesNotExist errors in post_delete signals.
    """
    try:
        if hasattr(instance, 'property') and instance.property:
            instance._cached_property_id = instance.property.id
            instance._cached_property_is_onboarded = instance.property.is_onboarded
        else:
            instance._cached_property_id = None
            instance._cached_property_is_onboarded = False
    except Exception:
        instance._cached_property_id = None
        instance._cached_property_is_onboarded = False

@receiver(post_delete, sender=Photo)
def handle_property_cover_image_on_delete(sender, instance, **kwargs):
    """
    Handle updating the property cover image when a photo is deleted.

    This signal:
    1. Triggers an asynchronous task to process the cover image
    2. Only processes property photos (not room photos)
    """
    try:
        # Use cached property information to avoid DoesNotExist errors
        if not hasattr(instance, '_cached_property_id') or not instance._cached_property_id:
            return

        property_id = instance._cached_property_id
        
        # Verify property still exists
        try:
            Property.objects.get(id=property_id)
        except Property.DoesNotExist:
            # Property was deleted, no need to process cover image
            return

        # Trigger the asynchronous task to process the cover image
        from apps.stay.tasks import process_property_cover_image
        logger.info(f"Triggering cover image processing for property {property_id} after photo deletion")
        process_property_cover_image.delay(
            property_id=str(property_id),
            photo_id=str(instance.id),
            action="delete"
        )

    except Exception as e:
        logger.error(f"Error in handle_property_cover_image_on_delete: {str(e)}", exc_info=True)


@receiver(pre_save, sender=Photo)
def handle_photo_updates(sender, instance, **kwargs):
    """
    Reset is_onboarded flag when a photo is modified to ensure it gets
    redeployed to SU API with the latest changes.
    """
    if instance.pk:  # If this is an update, not a new photo
        try:
            original = Photo.objects.get(pk=instance.pk)
            # If the image field has changed, reset the onboarded status
            if original.image != instance.image:
                instance.is_onboarded = False
        except Photo.DoesNotExist:
            pass

@receiver(m2m_changed, sender=Property.staffs.through)
def handle_staff_changes(sender, instance, action, pk_set, **kwargs):
    """
    Monitor changes to Property.staffs M2M relationship.
    Delete property if all staff members have been removed.
    """
    if action == "post_remove" or action == "post_clear":
        # Check if the property has any remaining staff
        if not instance.staffs.exists():
            # Delete the property since it has no staff
            instance.delete()

@receiver(pre_delete, sender='users.User')
def handle_user_deletion(sender, instance, **kwargs):
    """
    When a user is deleted, check their associated properties.
    Delete any properties where they are the last remaining staff member.
    """
    # Get all properties where this user is a staff member
    properties_to_check = Property.objects.filter(staffs=instance)

    for property_obj in properties_to_check:
        # Count remaining staff excluding the user being deleted
        remaining_staff = property_obj.staffs.exclude(id=instance.id).count()

        if remaining_staff == 0:
            # This user is the last staff member, delete the property
            property_obj.delete()

@receiver(post_save, sender='stay.Location')
def location_change_notification(sender, instance, **kwargs):
    """Send notification when location data is changed and associated property is onboarded"""
    try:
        # Skip if this is just an is_editable update from the Property signal
        if hasattr(instance, '_skip_update') or ('update_fields' in kwargs and kwargs['update_fields'] == ['is_editable']):
            return

        # Check if location is associated with any properties
        associated_properties = instance.property_set.all()
        if not associated_properties.exists():
            return

        # Filter for onboarded properties
        onboarded_properties = associated_properties.filter(is_onboarded=True)
        if not onboarded_properties.exists():
            return

        # Get the first staff member from any of the associated properties
        user = next((prop.staffs.first() for prop in associated_properties if prop.staffs.exists()), None)

        # Send email notification
        email_service = PropertyEmailService()
        email_service.send_location_change_notification(
            location=instance,
            onboarded_properties=onboarded_properties,
            user=user
        )

        logger.info(f"Location change notification sent for location ID: {instance.id}")

    except Exception as e:
        logger.error(f"Failed to send location change notification: {str(e)}", exc_info=True)

@receiver(pre_save, sender=TeamInvite)
def handle_team_invite(sender, instance, **kwargs):
    """
    Signal handler to update TeamInvite fields before saving.
    Sets user, registration status, and expiry date.
    Validates expiry status.
    """

    User = get_user_model()

    existing_user = User.objects.filter(email=instance.email).first()
    instance.user = existing_user if existing_user else None
    instance.is_registered = bool(instance.user)

    if not instance.expires_at:
        instance.expires_at = timezone.now() + timezone.timedelta(days=3)

    if instance.is_expired():
        raise ValidationError(_("This invite has expired."))



@receiver(post_save, sender=Property)
@receiver(post_save, sender=Room)
@receiver(post_save, sender=Photo)
@receiver(post_save, sender=GuestArrivalInfo)
@receiver(post_save, sender=PropertyMetadata)
@receiver(m2m_changed, sender=Property.amenities.through)
def update_property_setup_completion(sender, instance, **kwargs):
    """
    Signal handler to update property setup completion status when related models are saved.
    This ensures the is_setup_complete field is always up-to-date.
    """
    try:
        # Get the property instance based on the sender
        if sender == Property:
            property_instance = instance
        elif sender == PropertyMetadata:
            property_instance = instance.property
        elif sender == GuestArrivalInfo:
            property_instance = instance.property
        elif sender == Room:
            property_instance = instance.property
        elif sender == Photo and instance.property:
            property_instance = instance.property
        elif sender == Property.amenities.through:
            # For M2M changes, instance is the Property
            property_instance = instance
        else:
            return

        # Skip if this is PropertyMetadata creation to avoid recursion
        if sender == PropertyMetadata and kwargs.get('created', False):
            return

        # Update the property setup status
        is_complete, status_data = update_property_setup_status(property_instance)

        # Log detailed status information for debugging
        if not is_complete:
            logger.debug(f"Property {property_instance.id} setup incomplete. Status: {status_data}")

    except Exception as e:
        logger.error(f"Error updating property setup status: {str(e)}", exc_info=True)
