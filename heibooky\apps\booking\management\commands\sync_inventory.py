from django.core.management.base import BaseCommand, CommandError
from apps.booking.tasks.inventory import sync_inventory_for_empty_dates
from apps.stay.models import Property
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Synchronize inventory for dates that don\'t have bookings, room rates, or booking blocks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--property',
            type=str,
            help='Property ID to sync (if not provided, syncs all onboarded properties)',
        )
        parser.add_argument(
            '--days-ahead',
            type=int,
            default=365,
            help='Number of days ahead to check and sync (default: 365)',
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Run the task asynchronously using Celery',
        )
        parser.add_argument(
            '--list-properties',
            action='store_true',
            help='List all onboarded properties and their details',
        )

    def handle(self, *args, **options):
        if options['list_properties']:
            self.list_properties()
            return

        property_id = options['property']
        days_ahead = options['days_ahead']
        use_async = options['async']

        self.stdout.write(
            self.style.SUCCESS(
                f'Starting inventory synchronization for {days_ahead} days ahead...'
            )
        )

        if property_id:
            # Validate property exists and is onboarded
            try:
                property_instance = Property.objects.get(id=property_id)
                if not property_instance.is_onboarded:
                    raise CommandError(f'Property {property_id} is not onboarded')
                
                self.stdout.write(f'Syncing property: {property_instance.name} ({property_id})')
                
            except Property.DoesNotExist:
                raise CommandError(f'Property {property_id} not found')
        else:
            # Count total onboarded properties
            total_properties = Property.objects.filter(is_onboarded=True).count()
            self.stdout.write(f'Syncing all {total_properties} onboarded properties')

        if use_async:
            # Run asynchronously using Celery
            if property_id:
                result = sync_inventory_for_empty_dates.delay(property_id, days_ahead)
                self.stdout.write(
                    self.style.SUCCESS(f'Task queued with ID: {result.id}')
                )
            else:
                result = sync_inventory_for_empty_dates.delay(days_ahead=days_ahead)
                self.stdout.write(
                    self.style.SUCCESS(f'Task queued with ID: {result.id}')
                )
        else:
            # Run synchronously
            try:
                if property_id:
                    result = sync_inventory_for_empty_dates(property_id, days_ahead)
                else:
                    result = sync_inventory_for_empty_dates(days_ahead=days_ahead)
                
                self.display_results(result)
                
            except Exception as e:
                logger.error(f'Error during inventory sync: {str(e)}', exc_info=True)
                raise CommandError(f'Inventory sync failed: {str(e)}')

    def list_properties(self):
        """List all onboarded properties with their details."""
        properties = Property.objects.filter(is_onboarded=True).select_related('location')
        
        if not properties.exists():
            self.stdout.write(
                self.style.WARNING('No onboarded properties found')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f'Found {properties.count()} onboarded properties:')
        )
        self.stdout.write('')

        for prop in properties:
            # Count active rooms
            active_rooms = prop.rooms.filter(is_active=True).count()
            
            # Get location info
            location_info = 'N/A'
            if hasattr(prop, 'location') and prop.location:
                location_info = f'{prop.location.city}, {prop.location.country}'
            
            self.stdout.write(f'  ID: {prop.id}')
            self.stdout.write(f'  Name: {prop.name}')
            self.stdout.write(f'  Hotel ID: {prop.hotel_id}')
            self.stdout.write(f'  Location: {location_info}')
            self.stdout.write(f'  Active Rooms: {active_rooms}')
            self.stdout.write(f'  Created: {prop.created_at.strftime("%Y-%m-%d")}')
            self.stdout.write('')

    def display_results(self, result):
        """Display the results of the inventory sync operation."""
        if not result:
            self.stdout.write(
                self.style.ERROR('No result returned from sync operation')
            )
            return

        status = result.get('status', 'unknown')
        
        if status == 'success':
            style = self.style.SUCCESS
        elif status == 'warning':
            style = self.style.WARNING
        elif status == 'partial_failure':
            style = self.style.WARNING
        else:
            style = self.style.ERROR

        self.stdout.write('')
        self.stdout.write(style('=== INVENTORY SYNC RESULTS ==='))
        self.stdout.write(f'Status: {status.upper()}')
        
        if 'processed_properties' in result:
            self.stdout.write(f'Properties processed: {result["processed_properties"]}/{result["total_properties"]}')
            self.stdout.write(f'Total rooms processed: {result["total_rooms_processed"]}')
            self.stdout.write(f'Total dates synced: {result["total_dates_synced"]}')
            
            if result.get('date_range'):
                date_range = result['date_range']
                self.stdout.write(f'Date range: {date_range["start_date"]} to {date_range["end_date"]}')

        if result.get('failed_properties'):
            self.stdout.write('')
            self.stdout.write(self.style.ERROR('Failed properties:'))
            for failure in result['failed_properties']:
                self.stdout.write(f'  - Property {failure["property_id"]}: {failure["error"]}')

        if result.get('message'):
            self.stdout.write(f'Message: {result["message"]}')

        self.stdout.write('')
