"""
Tests for dashboard API endpoints.

This module contains comprehensive tests for the dashboard unread counts endpoint,
including authentication, data filtering, and error handling scenarios.
"""

from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.users.models import User
from apps.stay.models import Property, Location
from apps.reviews.models import Review
from apps.integrations.models import Notification
from apps.support.models import Chat, SupportMessage
from django.utils import timezone
import uuid


class DashboardUnreadCountsAPITestCase(TestCase):
    """Test cases for the dashboard unread counts API endpoint."""
    
    def setUp(self):
        """Set up test data for dashboard tests."""
        # Create test users
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpassword123'
        )
        
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            name='Other User',
            password='testpassword123'
        )
        
        # Create test location
        self.location = Location.objects.create(
            street='Test Street',
            city='Test City',
            post_code='12345',
            country='IT',
            latitude=41.9028,
            longitude=12.4964
        )
        
        # Create test property
        self.property = Property.objects.create(
            name='Test Property',
            property_type=Property.HOTEL,
            location=self.location
        )
        
        # Add user as staff to the property
        self.property.staffs.add(self.user)
        
        # Create another property for other user
        self.other_property = Property.objects.create(
            name='Other Property',
            property_type=Property.HOTEL,
            location=self.location
        )
        self.other_property.staffs.add(self.other_user)
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Define the API endpoint
        self.url = reverse('dashboard-unread-counts')
    
    def test_endpoint_requires_authentication(self):
        """Test that the endpoint requires authentication."""
        # Create unauthenticated client
        unauthenticated_client = APIClient()
        response = unauthenticated_client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_empty_unread_counts(self):
        """Test endpoint returns zero counts when no unread items exist."""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unread_reviews_count'], 0)
        self.assertEqual(response.data['unread_notifications_count'], 0)
        self.assertEqual(response.data['unread_support_messages_count'], 0)
    
    def test_unread_reviews_count(self):
        """Test that unread reviews are counted correctly for user's properties."""
        # Create unread review for user's property
        Review.objects.create(
            external_review_id='review1',
            property=self.property,
            reviewer_id='reviewer1',
            reviewer_role=Review.ReviewerRole.GUEST,
            reviewee_id='reviewee1',
            reviewee_role=Review.ReviewerRole.HOST,
            channel_id='1',
            is_read=False,
            submitted_at=timezone.now()
        )
        
        # Create read review for user's property (should not be counted)
        Review.objects.create(
            external_review_id='review2',
            property=self.property,
            reviewer_id='reviewer2',
            reviewer_role=Review.ReviewerRole.GUEST,
            reviewee_id='reviewee2',
            reviewee_role=Review.ReviewerRole.HOST,
            channel_id='1',
            is_read=True,
            submitted_at=timezone.now()
        )
        
        # Create unread review for other user's property (should not be counted)
        Review.objects.create(
            external_review_id='review3',
            property=self.other_property,
            reviewer_id='reviewer3',
            reviewer_role=Review.ReviewerRole.GUEST,
            reviewee_id='reviewee3',
            reviewee_role=Review.ReviewerRole.HOST,
            channel_id='1',
            is_read=False,
            submitted_at=timezone.now()
        )
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unread_reviews_count'], 1)
    
    def test_unread_notifications_count(self):
        """Test that unread notifications are counted correctly for the user."""
        # Create unread notification for user
        Notification.objects.create(
            user=self.user,
            title='Test Notification 1',
            message='Test message 1',
            is_read=False
        )
        
        # Create read notification for user (should not be counted)
        Notification.objects.create(
            user=self.user,
            title='Test Notification 2',
            message='Test message 2',
            is_read=True
        )
        
        # Create unread notification for other user (should not be counted)
        Notification.objects.create(
            user=self.other_user,
            title='Test Notification 3',
            message='Test message 3',
            is_read=False
        )
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unread_notifications_count'], 1)
    
    def test_unread_support_messages_count(self):
        """Test that unread support messages are counted correctly for the user."""
        # Create chat for user
        chat = Chat.objects.create(
            user=self.user,
            status='pending'
        )
        
        # Create unread message from support
        SupportMessage.objects.create(
            chat=chat,
            message='Support message 1',
            is_from_support=True,
            is_read=False
        )
        
        # Create read message from support (should not be counted)
        SupportMessage.objects.create(
            chat=chat,
            message='Support message 2',
            is_from_support=True,
            is_read=True
        )
        
        # Create unread message from user (should not be counted)
        SupportMessage.objects.create(
            chat=chat,
            message='User message',
            is_from_support=False,
            is_read=False
        )
        
        # Create chat for other user
        other_chat = Chat.objects.create(
            user=self.other_user,
            status='pending'
        )
        
        # Create unread message from support for other user (should not be counted)
        SupportMessage.objects.create(
            chat=other_chat,
            message='Support message for other user',
            is_from_support=True,
            is_read=False
        )
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unread_support_messages_count'], 1)
    
    def test_combined_unread_counts(self):
        """Test that all unread counts work together correctly."""
        # Create unread review
        Review.objects.create(
            external_review_id='review1',
            property=self.property,
            reviewer_id='reviewer1',
            reviewer_role=Review.ReviewerRole.GUEST,
            reviewee_id='reviewee1',
            reviewee_role=Review.ReviewerRole.HOST,
            channel_id='1',
            is_read=False,
            submitted_at=timezone.now()
        )
        
        # Create unread notifications
        for i in range(3):
            Notification.objects.create(
                user=self.user,
                title=f'Test Notification {i+1}',
                message=f'Test message {i+1}',
                is_read=False
            )
        
        # Create unread support messages
        chat = Chat.objects.create(user=self.user, status='pending')
        for i in range(2):
            SupportMessage.objects.create(
                chat=chat,
                message=f'Support message {i+1}',
                is_from_support=True,
                is_read=False
            )
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unread_reviews_count'], 1)
        self.assertEqual(response.data['unread_notifications_count'], 3)
        self.assertEqual(response.data['unread_support_messages_count'], 2)
    
    def test_response_format(self):
        """Test that the response has the correct format and field names."""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that all required fields are present
        required_fields = [
            'unread_reviews_count',
            'unread_notifications_count',
            'unread_support_messages_count'
        ]
        
        for field in required_fields:
            self.assertIn(field, response.data)
            self.assertIsInstance(response.data[field], int)
            self.assertGreaterEqual(response.data[field], 0)
    
    def test_superuser_sees_all_reviews(self):
        """Test that superuser can see all unread reviews, not just their properties."""
        # Create superuser
        superuser = User.objects.create_superuser(
            email='<EMAIL>',
            name='Admin User',
            password='adminpassword123'
        )
        
        # Create unread reviews for different properties
        Review.objects.create(
            external_review_id='review1',
            property=self.property,
            reviewer_id='reviewer1',
            reviewer_role=Review.ReviewerRole.GUEST,
            reviewee_id='reviewee1',
            reviewee_role=Review.ReviewerRole.HOST,
            channel_id='1',
            is_read=False,
            submitted_at=timezone.now()
        )
        
        Review.objects.create(
            external_review_id='review2',
            property=self.other_property,
            reviewer_id='reviewer2',
            reviewer_role=Review.ReviewerRole.GUEST,
            reviewee_id='reviewee2',
            reviewee_role=Review.ReviewerRole.HOST,
            channel_id='1',
            is_read=False,
            submitted_at=timezone.now()
        )
        
        # Test with superuser
        self.client.force_authenticate(user=superuser)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unread_reviews_count'], 2)  # Should see all reviews
