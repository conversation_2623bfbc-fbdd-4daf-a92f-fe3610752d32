import time
import logging
from django.utils.deprecation import MiddlewareMixin
from .metrics import REQUEST_COUNT, REQUEST_LATENCY, ACTIVE_REQUESTS, metrics_collector

logger = logging.getLogger(__name__)


class PrometheusMiddleware(MiddlewareMixin):
    """Middleware to collect Prometheus metrics for HTTP requests."""
    
    def process_request(self, request):
        """Start timing the request."""
        request._prometheus_start_time = time.time()
        request._prometheus_decremented = False
        ACTIVE_REQUESTS.inc()
        
    def process_response(self, request, response):
        """Collect metrics after processing response."""
        try:
            # Calculate request duration
            if hasattr(request, '_prometheus_start_time'):
                duration = time.time() - request._prometheus_start_time
                
                # Get endpoint from URL pattern
                endpoint = 'unknown'
                if hasattr(request, 'resolver_match') and request.resolver_match:
                    endpoint = request.resolver_match.view_name or 'unknown'
                
                # Record metrics
                REQUEST_COUNT.labels(
                    method=request.method,
                    status=response.status_code,
                    endpoint=endpoint
                ).inc()
                
                REQUEST_LATENCY.labels(
                    method=request.method,
                    endpoint=endpoint
                ).observe(duration)
                
                # Decrement active requests only if not already decremented
                if not getattr(request, '_prometheus_decremented', False):
                    ACTIVE_REQUESTS.dec()
                    request._prometheus_decremented = True
                
                # Log slow requests
                if duration > 2.0:  # Log requests taking more than 2 seconds
                    logger.warning(
                        f"Slow request detected: {request.method} {request.path} "
                        f"took {duration:.2f}s (status: {response.status_code})"
                    )
                    
        except Exception as e:
            logger.error(f"Error in PrometheusMiddleware: {e}")
            # Ensure we decrement only if not already decremented
            if not getattr(request, '_prometheus_decremented', False):
                ACTIVE_REQUESTS.dec()
                request._prometheus_decremented = True
            
        return response
    
    def process_exception(self, request, exception):
        """Handle exceptions."""
        try:
            if hasattr(request, '_prometheus_start_time'):
                duration = time.time() - request._prometheus_start_time
                
                endpoint = 'unknown'
                if hasattr(request, 'resolver_match') and request.resolver_match:
                    endpoint = request.resolver_match.view_name or 'unknown'
                
                REQUEST_COUNT.labels(
                    method=request.method,
                    status=500,
                    endpoint=endpoint
                ).inc()
                
                REQUEST_LATENCY.labels(
                    method=request.method,
                    endpoint=endpoint
                ).observe(duration)
                
                # Decrement active requests only if not already decremented
                if not getattr(request, '_prometheus_decremented', False):
                    ACTIVE_REQUESTS.dec()
                    request._prometheus_decremented = True
                
        except Exception as e:
            logger.error(f"Error in PrometheusMiddleware exception handler: {e}")
            # Ensure we decrement only if not already decremented
            if not getattr(request, '_prometheus_decremented', False):
                ACTIVE_REQUESTS.dec()
                request._prometheus_decremented = True


class MetricsCollectionMiddleware(MiddlewareMixin):
    """Middleware to periodically collect system metrics."""
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self._last_collection = 0
        self._collection_interval = 30  # Collect metrics every 30 seconds
        
    def process_request(self, request):
        """Collect metrics if enough time has passed."""
        current_time = time.time()
        if current_time - self._last_collection > self._collection_interval:
            try:
                metrics_collector.collect_all_metrics()
                self._last_collection = current_time
            except Exception as e:
                logger.error(f"Error collecting metrics: {e}")
                
        return None
