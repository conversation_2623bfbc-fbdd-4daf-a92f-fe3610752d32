# Generated by Django 5.1.2 on 2025-06-28 07:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('pricing', '0001_initial'),
        ('stay', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='rateplan',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_plans', to='stay.property'),
        ),
        migrations.AddField(
            model_name='roomrate',
            name='rate_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_rates', to='pricing.rateplan'),
        ),
        migrations.AddField(
            model_name='roomrate',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_rates', to='stay.room'),
        ),
    ]
