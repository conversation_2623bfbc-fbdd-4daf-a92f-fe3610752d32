"""
WebSocket health check endpoint for diagnosing connection issues
"""

import redis
from django.http import JsonResponse
from django.views import View
from django.conf import settings
from channels.layers import get_channel_layer


class WebSocketHealthView(View):
    """Health check endpoint for WebSocket infrastructure"""
    
    def get(self, request):
        """Return health status of WebSocket components"""
        health_status = {
            "websocket": "ok",
            "channel_layer": "unknown",
            "redis": "unknown",
            "cors_origins": [],
            "debug_mode": settings.DEBUG,
            "asgi_application": getattr(settings, 'ASGI_APPLICATION', 'Not configured')
        }
        
        # Check channel layer
        try:
            channel_layer = get_channel_layer()
            if channel_layer:
                backend_name = channel_layer.__class__.__name__
                health_status["channel_layer"] = f"ok ({backend_name})"
                
                # Test channel layer functionality
                if hasattr(channel_layer, 'send'):
                    health_status["channel_layer_functional"] = "ok"
                else:
                    health_status["channel_layer_functional"] = "limited"
            else:
                health_status["channel_layer"] = "not configured"
        except Exception as e:
            health_status["channel_layer"] = f"error: {str(e)}"
        
        # Check Redis if configured
        redis_url = getattr(settings, 'REDIS_URL', None)
        if redis_url:
            try:
                r = redis.from_url(redis_url)
                response = r.ping()
                if response:
                    health_status["redis"] = "ok"
                    
                    # Test basic Redis operations
                    try:
                        r.set('health_check', 'test', ex=5)
                        value = r.get('health_check')
                        if value == b'test':
                            health_status["redis_operations"] = "ok"
                            r.delete('health_check')
                        else:
                            health_status["redis_operations"] = "failed"
                    except Exception as e:
                        health_status["redis_operations"] = f"error: {str(e)}"
                else:
                    health_status["redis"] = "ping failed"
            except Exception as e:
                health_status["redis"] = f"error: {str(e)}"
        else:
            if settings.DEBUG:
                health_status["redis"] = "not configured (using InMemory for development)"
            else:
                health_status["redis"] = "not configured (required for production)"
        
        # Check CORS configuration
        cors_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
        health_status["cors_origins"] = cors_origins
        health_status["cors_origins_count"] = len(cors_origins)
        
        # Check if channels is installed
        try:
            import channels
            health_status["channels_version"] = channels.__version__
        except ImportError:
            health_status["channels_version"] = "not installed"
        
        # Check if channels_redis is installed (for production)
        try:
            import channels_redis
            health_status["channels_redis_version"] = channels_redis.__version__
        except ImportError:
            if not settings.DEBUG:
                health_status["channels_redis_version"] = "not installed (required for production)"
            else:
                health_status["channels_redis_version"] = "not installed (optional for development)"
        
        # Overall health assessment
        issues = []
        if health_status["channel_layer"].startswith("error"):
            issues.append("channel_layer_error")
        if health_status["redis"].startswith("error"):
            issues.append("redis_error")
        if not cors_origins:
            issues.append("no_cors_origins")
        if health_status["channels_version"] == "not installed":
            issues.append("channels_not_installed")
        if not settings.DEBUG and health_status["channels_redis_version"].startswith("not installed"):
            issues.append("channels_redis_not_installed")
            
        health_status["overall_status"] = "healthy" if not issues else "issues_detected"
        health_status["issues"] = issues
        
        # HTTP status code based on health
        status_code = 200 if not issues else 503
        
        return JsonResponse(health_status, status=status_code)


class WebSocketConfigView(View):
    """Detailed WebSocket configuration information"""
    
    def get(self, request):
        """Return detailed WebSocket configuration"""
        config_info = {
            "asgi_application": getattr(settings, 'ASGI_APPLICATION', 'Not configured'),
            "debug_mode": settings.DEBUG,
            "installed_apps": {
                "channels": "channels" in settings.INSTALLED_APPS,
                "corsheaders": "corsheaders" in settings.INSTALLED_APPS,
            },
            "middleware": list(settings.MIDDLEWARE),
            "cors_settings": {
                "allowed_origins": getattr(settings, 'CORS_ALLOWED_ORIGINS', []),
                "allow_credentials": getattr(settings, 'CORS_ALLOW_CREDENTIALS', False),
                "allow_all_origins": getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False),
            },
            "channel_layers": getattr(settings, 'CHANNEL_LAYERS', {}),
            "redis_url": getattr(settings, 'REDIS_URL', 'Not configured'),
        }
        
        # Security check - only show detailed config to staff users
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({
                "error": "Access denied. Staff privileges required."
            }, status=403)
        
        return JsonResponse(config_info)


class WebSocketTestView(View):
    """Simple WebSocket connection test endpoint"""
    
    def get(self, request):
        """Return WebSocket connection test information"""
        chat_id = request.GET.get('chat_id')
        
        if not chat_id:
            return JsonResponse({
                "error": "chat_id parameter required"
            }, status=400)
        
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return JsonResponse({
                "error": "Authentication required"
            }, status=401)
        
        # Generate WebSocket URL
        protocol = 'wss' if request.is_secure() else 'ws'
        host = request.get_host()
        
        # Note: In a real implementation, you'd generate a proper JWT token
        # This is just for testing purposes
        ws_url = f"{protocol}://{host}/ws/support/{chat_id}/?token=YOUR_JWT_TOKEN"
        
        test_info = {
            "websocket_url": ws_url,
            "chat_id": chat_id,
            "user_id": str(request.user.id),
            "user_is_staff": request.user.is_staff,
            "protocol": protocol,
            "host": host,
            "instructions": {
                "1": "Replace YOUR_JWT_TOKEN with a valid JWT token",
                "2": "Use this URL to test WebSocket connection",
                "3": "Check browser console for connection errors",
                "4": "Use the diagnostic command for detailed testing"
            },
            "diagnostic_command": f"python manage.py diagnose_websocket --chat-id {chat_id} --token YOUR_JWT_TOKEN"
        }
        
        return JsonResponse(test_info)
