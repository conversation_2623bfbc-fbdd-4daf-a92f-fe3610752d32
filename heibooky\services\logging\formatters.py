import logging
import json
import traceback
from datetime import datetime
from django.utils.log import <PERSON><PERSON><PERSON><PERSON><PERSON>
import os
from .utils import get_client_ip


class StructuredFormatter(logging.Formatter):
    """
    Structured JSON formatter for logs that can be easily parsed by log aggregation systems.
    """
    
    def format(self, record):
        # Base log entry
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'process_id': os.getpid(),
            'thread_id': record.thread,
        }
        
        # Add request information if available
        request = getattr(record, 'request', None)
        if request:
            log_entry['request'] = {
                'method': request.method,
                'path': request.path,
                'full_path': request.get_full_path(),
                'remote_addr': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'referer': request.META.get('HTTP_REFERER', ''),
                'user_id': request.user.id if hasattr(request, 'user') and request.user.is_authenticated else None,
                'session_key': request.session.session_key if hasattr(request, 'session') else None,
            }
        
        # Add exception information if available
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields
        # Add extra fields
        MAX_EXTRA_FIELD_SIZE = 1000  # Configurable limit
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'message', 'exc_info', 'exc_text',
                          'stack_info', 'request']:
                log_entry['extra'] = log_entry.get('extra', {})
                # Truncate large values and convert objects safely
                if isinstance(value, str) and len(value) > MAX_EXTRA_FIELD_SIZE:
                    value = value[:MAX_EXTRA_FIELD_SIZE] + '...(truncated)'
                log_entry['extra'][key] = value
        
        return json.dumps(log_entry, default=str)


class DetailedRequestFormatter(ServerFormatter):
    """
    Enhanced request formatter with more detailed information.
    """
    
    def format(self, record):
        request = getattr(record, 'request', None)
        if request:
            record.source_ip = get_client_ip(request)
            record.http_method = request.method
            record.requested_url = request.get_full_path()
            record.user_agent = request.META.get('HTTP_USER_AGENT', '')[:200]  # Truncate long user agents
            record.request_headers = {
                'Referer': request.META.get('HTTP_REFERER', ''),
                'Accept-Language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
                'Content-Type': request.META.get('CONTENT_TYPE', ''),
                'X-Forwarded-For': request.META.get('HTTP_X_FORWARDED_FOR', ''),
                'X-Real-IP': request.META.get('HTTP_X_REAL_IP', ''),
                # Note: Avoid logging sensitive headers like Authorization, Cookie, X-API-Key
            }            
            record.user_id = request.user.id if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous'
            record.session_id = request.session.session_key if hasattr(request, 'session') else ''
            record.server_ip = request.META.get('SERVER_NAME', '')
            record.process_id = os.getpid()
            
            # Add request timing if available
            if hasattr(request, '_start_time'):
                import time
                record.request_duration = time.time() - request._start_time
            else:
                record.request_duration = ''
                
        else:
            record.source_ip = ''
            record.http_method = ''
            record.requested_url = ''
            record.user_agent = ''
            record.request_headers = {}
            record.user_id = ''
            record.session_id = ''
            record.server_ip = ''
            record.process_id = os.getpid()
            record.request_duration = ''

        return super().format(record)


class PerformanceFormatter(logging.Formatter):
    """
    Formatter for performance-related logs.
    """
    
    def format(self, record):
        # Add performance metrics if available
        if hasattr(record, 'duration'):
            record.duration_ms = f"{record.duration * 1000:.2f}ms"
        
        if hasattr(record, 'memory_usage'):
            record.memory_mb = f"{record.memory_usage / 1024 / 1024:.2f}MB"
            
        return super().format(record)


class SecurityFormatter(logging.Formatter):
    """
    Formatter for security-related logs with additional security context.
    """
    
    def format(self, record):
        request = getattr(record, 'request', None)
        if request:
            # Add security-relevant information
            record.security_context = {
                'ip': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'auth_user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
                'is_secure': request.is_secure(),
                'method': request.method,
                'path': request.path,
            }
        else:
            record.security_context = {}
        
        return super().format(record)
