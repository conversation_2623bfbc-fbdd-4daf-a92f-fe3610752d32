from django.db import models
import uuid
from django.utils.translation import gettext_lazy as _
from datetime import datetime
from phonenumber_field import modelfields
from django.core.exceptions import ValidationError
from django.core.files.storage import storages

class Property(models.Model):
    """
    Model to manage basic information about a property.
    """
    HOTEL = 1
    MOTEL = 2
    VACATIONAL_RENTAL = 3

    PROPERTY_TYPE_CHOICES = [
        (HOTEL, 'Hotel'),
        (MOTEL, 'Motel'),
        (VACATIONAL_RENTAL, 'Vacational Rental'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hotel_id = models.CharField(max_length=8, editable=False, blank=True, null=True)
    chain_id = models.CharField(max_length=8, editable=False, blank=True, null=True)
    name = models.CharField(max_length=255)
    property_type = models.IntegerField(choices=PROPERTY_TYPE_CHOICES, default=HOTEL)
    description = models.TextField(blank=True, null=True)
    location = models.ForeignKey('stay.Location', on_delete=models.CASCADE)
    cover_image = models.ImageField(upload_to='property_cover_images/', blank=True, null=True, storage=storages['photo_storage'])
    is_multi_unit = models.BooleanField(default=False)
    is_onboarded = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    staffs = models.ManyToManyField('users.User', related_name="properties")

    class Meta:
        verbose_name_plural = "Properties"

    def __str__(self):
        return self.name

    def get_property_type_display(self, use_numeric=False):
        """
        Returns the property type as a string or numeric value based on the `use_numeric` flag.
        """
        if use_numeric:
            return self.property_type
        return dict(self.PROPERTY_TYPE_CHOICES).get(self.property_type, "Unknown")


class PropertyOwnership(models.Model):
    """
    Model to store the legal relationship between users and properties.
    """
    class LegalRelation(models.TextChoices):
        LEGAL_OWNER = 'owner', _('Legal owner')
        TENANT_ADMINISTRATOR = 'tenant_admin', _('Tenant administrator')
        INTERMEDIARY_ADMINISTRATOR = 'intermediary_admin', _('Intermediary administrator')

    user = models.ForeignKey('users.User', on_delete=models.CASCADE)
    property = models.OneToOneField(Property, on_delete=models.CASCADE, related_name='ownership')
    relation_type = models.CharField(max_length=20, choices=LegalRelation.choices)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.name} - {self.property.name} ({self.get_relation_type_display()})"

class PropertyMetadata(models.Model):
    """
    Model to store additional property information (metadata) not covered in the Property model.
    """

    NON_REFUNDABLE = 'non_refundable'
    PARTIALLY_REFUNDABLE = 'partially_refundable'
    FULLY_REFUNDABLE = 'fully_refundable'

    CANCELATION_POLICY_CHOICES = [
        (NON_REFUNDABLE, 'Non refundable'),
        (PARTIALLY_REFUNDABLE, 'Partially refundable'),
        (FULLY_REFUNDABLE, 'Fully refundable'),
    ]


    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.OneToOneField('stay.Property', on_delete=models.CASCADE, related_name='metadata')

    check_in_time = models.TimeField(_('Check-in from'), default='16:00')
    check_out_time = models.TimeField(_('Check-out'), default='10:00')
    close_out_days = models.CharField(_('Close-out days'), max_length=2, blank=True, null=True)
    close_out_time = models.TimeField(_('Close-out time'), blank=True, null=True)

    cancelation_policy_type = models.CharField(max_length=20, choices=CANCELATION_POLICY_CHOICES, default=NON_REFUNDABLE)

    regional_id_code = models.CharField(_('Regional Identification Code'), max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Setup completion tracking
    is_setup_complete = models.BooleanField(default=False, verbose_name=_('Setup Complete'))
    last_reminder_sent = models.DateTimeField(null=True, blank=True, verbose_name=_('Last Reminder Sent'))
    reminder_count = models.PositiveSmallIntegerField(default=0, verbose_name=_('Reminder Count'))

    def __str__(self):
        return f"Metadata for {self.property.name}"

    class Meta:
        verbose_name_plural = "Property Metadata"

class Amenity(models.Model):
    """
    Model to store global amenities that can be linked to properties.
    """
    name = models.CharField(max_length=255, unique=True)
    category = models.CharField(max_length=100)
    properties = models.ManyToManyField(
        'stay.Property',
        through='PropertyAmenity',
        related_name='amenities'
    )

    def __str__(self):
        return f"{self.name} ({self.category})"

    def save(self, *args, **kwargs):
        # Normalize name and category to prevent duplicates
        self.name = self.name.strip().lower()
        self.category = self.category.strip().lower()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "Amenities"
        unique_together = ['name', 'category']
        ordering = ['category', 'name']
        # Add indexes for case-insensitive lookups
        indexes = [
            models.Index(fields=['name', 'category']),
        ]

class PropertyAmenity(models.Model):
    """
    Through model to connect properties with amenities and track availability.
    """
    property = models.ForeignKey('stay.Property', on_delete=models.CASCADE)
    amenity = models.ForeignKey(Amenity, on_delete=models.CASCADE)
    is_available = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.amenity.name} for {self.property.name}"

    class Meta:
        verbose_name_plural = "Property Amenities"
        unique_together = ['property', 'amenity']

class Room(models.Model):
    """
    Model to represent rooms in a property (e.g., bedroom, apartment).
    """
    # Room Type Choices based on provided codes
    ROOM_TYPE_CHOICES = [
        ('1', 'Apartment'),
        ('4', 'Quadruple'),
        ('5', 'Suite'),
        ('7', 'Triple'),
        ('8', 'Twin'),
        ('9', 'Double'),
        ('10', 'Single'),
        ('12', 'Studio'),
        ('13', 'Family'),
        ('25', 'Dormitory room'),
        ('26', 'Bed in Dormitory'),
        ('27', 'Bungalow'),
        ('28', 'Chalet'),
        ('29', 'Holiday home'),
        ('31', 'Villa'),
        ('32', 'Mobile home'),
        ('33', 'Tent'),
        ('34', 'Powered/Unpowered Site'),
        ('35', 'King'),
        ('36', 'Queen')
    ]

    # Size Measurement Unit Choices
    SIZE_MEASUREMENT_UNIT_CHOICES = [
        ('sqm', 'Square Meters'),
        ('sqft', 'Square Feet')
    ]
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey('Property', on_delete=models.CASCADE, related_name='rooms')
    room_type = models.CharField(max_length=2, choices=ROOM_TYPE_CHOICES)
    room_rate = models.DecimalField(max_digits=15, decimal_places=2)
    max_occupancy = models.IntegerField()
    max_child_occupancy = models.IntegerField()
    quantity = models.IntegerField(default=1)
    size_measurement = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    size_measurement_unit = models.CharField(
        max_length=4,
        choices=SIZE_MEASUREMENT_UNIT_CHOICES,
        blank=True,
        null=True
    )
    description = models.TextField(blank=True, null=True, max_length=2000)
    bed_config = models.JSONField(default=dict, null=True, blank=True, help_text="bed structure fot room configuration")
    bathroom_quntity = models.IntegerField(default=1)
    is_active = models.BooleanField(default=True)
    is_onboarded = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_room_type_display()} - {self.property.name or 'Unnamed'}"

    def clean(self):
        if self.max_occupancy > 999:
            raise ValidationError('Maximum occupancy cannot exceed 999')
        if self.max_child_occupancy >= self.max_occupancy:
            raise ValidationError('Maximum child occupancy must be less than maximum occupancy')

    def save(self, *args, **kwargs):
        self.clean()
        # Check if this is a new record (no primary key yet)
        if not self.pk:
            # Check if the property allows multiple units
            if not self.property.is_multi_unit:
                if Room.objects.filter(property=self.property).exists():
                    raise ValueError("Cannot create more than one room for a property that is not multi-unit.")

        super().save(*args, **kwargs)


class RoomAmenity(models.Model):
    """
    Model to define amenities available in each room.
    """
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='amenities')
    amenity = models.ForeignKey(Amenity, on_delete=models.CASCADE)
    is_available = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.room.property.name}: {self.amenity.name}"

    class Meta:
        verbose_name_plural = "Room amenities"

class Photo(models.Model):
    """
    Model to manage photos related to properties and units.
    Enhanced with better error handling and validation.
    """
    MAX_PROPERTY_PHOTOS = 45
    ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']

    def generate_unique_filename(self, filename):
        """Generate a unique filename with timestamp and UUID"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # Extract extension safely
            if '.' in filename:
                ext = filename.split('.')[-1].lower()
            else:
                ext = 'jpg'  # Default extension if none provided
                logger.warning(f"No extension found in filename: {filename}, using default: jpg")

            # Validate extension
            if f'.{ext}' not in self.ALLOWED_EXTENSIONS:
                logger.warning(f"Invalid extension: {ext}, converting to jpg")
                ext = 'jpg'  # Default to jpg for invalid extensions

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            unique_id = str(uuid.uuid4().hex[:8])

            # Get prefix based on whether it's property or room photo
            if self.property and self.property.hotel_id:
                prefix = f"prop_{self.property.hotel_id}"
            elif self.room and self.room.id:
                prefix = f"room_{self.room.id[:8]}"
            else:
                prefix = "unknown"
                logger.warning(f"Unknown photo type (no property or room): {filename}")

            final_path = f"property_photos/{prefix}/{timestamp}_{unique_id}.{ext}"
            logger.info(f"Generated unique filename: {final_path} for original: {filename}")
            return final_path

        except Exception as e:
            logger.error(f"Error generating unique filename for {filename}: {str(e)}")
            # Fallback to a very basic filename pattern if something goes wrong
            return f"property_photos/error/{uuid.uuid4().hex}.jpg"

    @classmethod
    def validate_property_photo_limit(cls, property_instance):
        """Validate that a property doesn't exceed the maximum number of photos"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            current_count = cls.objects.filter(property=property_instance).count()
            logger.info(f"Checking photo limit for property {property_instance.id}: {current_count}/{cls.MAX_PROPERTY_PHOTOS}")

            if current_count >= cls.MAX_PROPERTY_PHOTOS:
                logger.warning(f"Photo limit reached for property {property_instance.id}: {current_count}/{cls.MAX_PROPERTY_PHOTOS}")
                raise ValidationError(
                    f"Maximum number of photos ({cls.MAX_PROPERTY_PHOTOS}) reached for this property. "
                    f"Current count: {current_count}."
                )
            return True
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error validating photo limit for property {property_instance.id}: {str(e)}")
            raise ValidationError(f"Error checking photo limit: {str(e)}")

    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name='photos', null=True, blank=True)
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='photos', null=True, blank=True)
    image = models.ImageField(upload_to=generate_unique_filename, storage=storages['photo_storage'])
    created_at = models.DateTimeField(auto_now_add=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    is_onboarded = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Photo"
        verbose_name_plural = "Photos"
        ordering = ['-created_at']

    def __str__(self):
        return f"Photo of {self.property or self.room}"

    def clean(self):
        """Validate the photo before saving"""
        import logging
        logger = logging.getLogger(__name__)

        # Validate that photo is linked to either property or room
        if not self.property and not self.room:
            logger.error("Photo validation failed: not linked to property or room")
            raise ValidationError("A photo must be linked to either a property or a room.")

        # Check property photo limit
        if self.property and not self.pk:  # Only check on new photo creation
            try:
                self.validate_property_photo_limit(self.property)
            except ValidationError as e:
                logger.error(f"Photo limit validation failed for property {self.property.id}: {str(e)}")
                raise

    def save(self, *args, **kwargs):
        import logging
        logger = logging.getLogger(__name__)

        try:
            # Run validation
            self.clean()

            # Set description if not provided
            if not self.description:
                if self.property:
                    self.description = f"Photo of {self.property.name}"
                elif self.room:
                    self.description = f"Photo of {self.room.property.name} - {self.room.get_room_type_display()}"

            # Save the photo
            super().save(*args, **kwargs)
            logger.info(f"Successfully saved photo ID: {self.id} for {'property' if self.property else 'room'}")

        except ValidationError as e:
            logger.error(f"Validation error saving photo: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error saving photo: {str(e)}")
            raise ValidationError(f"Error saving photo: {str(e)}")

class GuestArrivalInfo(models.Model):
    """
    Model to store guest arrival information for a property.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.OneToOneField(Property, on_delete=models.CASCADE, related_name="guest_arrival_info")
    contact_name = models.CharField(max_length=255)
    contact_surname = models.CharField(max_length=255)
    email = models.EmailField()
    phone_number = modelfields.PhoneNumberField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Guest Arrival Info for {self.property.name}"
