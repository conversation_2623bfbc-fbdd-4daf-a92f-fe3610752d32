# Generated by Django 5.1.2 on 2025-06-28 07:58

import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DownloadableTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('file', models.FileField(upload_to='templates/')),
                ('category', models.CharField(choices=[('checkin', 'Check-in'), ('Legal & Compliance', 'Legale e Conformità'), ('Terms & Conditions', 'Termini e Condizioni'), ('Privacy Policy', 'Politica sulla Privacy'), ('Marketing & Communication', 'Marketing e Comunicazione'), ('Branding & Design', 'Marchio e Design'), ('Operations & Management', 'Operazioni e Gestione'), ('Financial & Accounting', 'Finanza e Contabilità'), ('Human Resources', 'Risorse Umane'), ('Technology & IT', 'Tecnologia e IT'), ('Safety & Security', 'Sicurezza e Protezione'), ('Health & Well-being', 'Salute e Benessere'), ('Environment & Sustainability', 'Ambiente e Sostenibilità'), ('Guest Experience', 'Esperienza Ospiti'), ('other', 'Altro')], max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='invoices/%Y/%m/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('digithera_reference', models.CharField(blank=True, max_length=255)),
                ('sdi_status', models.CharField(choices=[('pending', 'Pending'), ('failed', 'Failed'), ('uploaded', 'Uploaded')], default='pending', max_length=50)),
                ('progressive_number', models.CharField(editable=False, max_length=10, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Payout',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_payment_intent_id', models.CharField(max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='eur', max_length=10)),
                ('status', models.CharField(choices=[('failed', 'Failed'), ('pending', 'Pending'), ('successful', 'Successful')], default='pending', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('invoice_status', models.BooleanField(default=False)),
                ('processing_errors', models.JSONField(default=dict, help_text='Log of processing errors')),
            ],
        ),
        migrations.CreateModel(
            name='PropertyOnlineCheckIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_enabled', models.BooleanField(default=False, verbose_name='Online Check-in Enabled')),
                ('istat_enabled', models.BooleanField(default=False, verbose_name='ISTAT Reporting Enabled')),
                ('alloggati_enabled', models.BooleanField(default=False, verbose_name='Alloggati Web Reporting Enabled')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='Last Synchronization')),
                ('next_sync', models.DateTimeField(blank=True, null=True, verbose_name='Next Scheduled Synchronization')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Property Online Check-in',
                'verbose_name_plural': 'Property Online Check-ins',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Sequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('value', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='StripeCustomer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_customer_id', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SUAPIActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_id', models.CharField(blank=True, help_text='ID of the property involved in the action', max_length=255, null=True)),
                ('action', models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('batch', 'Batch'), ('sync', 'Sync')], max_length=20)),
                ('description', models.TextField(help_text='Description of the action taken')),
                ('status', models.CharField(choices=[('successful', 'Successful'), ('failed', 'Failed'), ('partial', 'Partial')], max_length=10)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('details', models.JSONField(blank=True, help_text='Additional data related to the action, such as API response', null=True)),
            ],
            options={
                'verbose_name': 'SU API Action Log',
                'verbose_name_plural': 'SU API Action Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
