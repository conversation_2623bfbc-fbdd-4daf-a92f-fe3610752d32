# Environment Configuration Example
# Copy this file to .env and replace placeholder values with actual credentials
# NEVER commit .env files to version control

# D<PERSON>go Settings
ADMIN_EMAIL='<EMAIL>'
SECRET_KEY=your-secret-key-here
DEBUG=False

# Database Configuration  
DB_NAME=your_database_name
DB_PASSWORD=your_secure_database_password
DB_USER=your_database_user
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password

# Domain and Security
ALLOWED_HOSTS=yourdomain.com,localhost,127.0.0.1
FRONTEND_URL='https://yourdomain.com'
ADMIN_URL='https://yourdomain.com/admin'
SUPPORT_URL='https://support.yourdomain.com'

# AWS/CDN Configuration
AWS_SECRET_ACCESS_KEY='your-aws-secret-key'
AWS_ACCESS_KEY_ID='your-aws-access-key'
AWS_STORAGE_BUCKET_NAME='your-bucket-name'
AWS_S3_ENDPOINT_URL='https://your-s3-endpoint.com'
AWS_CDN_URL='https://cdn.yourdomain.com'

# Payment Processing
STRIPE_SECRET_KEY='sk_test_your_stripe_secret_key'
STRIPE_PUBLISHABLE_KEY='pk_test_your_stripe_publishable_key'

# Email Configuration
MAILGUN_API_KEY='your-mailgun-api-key'
MAILGUN_DOMAIN='yourdomain.com'
MAILGUN_SMTP_PASSWORD='your-mailgun-smtp-password'
EMAIL_HOST='smtp.eu.mailgun.org'
DEFAULT_FROM_EMAIL='<EMAIL>'

# SMTP Configuration for Monitoring/Alerts
SMTP_HOST='smtp.eu.mailgun.org'
SMTP_PORT='587'
SMTP_USERNAME='your-smtp-username'
SMTP_PASSWORD='your-smtp-password'
SMTP_FROM='<EMAIL>'

# Alert Email Recipients
ALERT_EMAIL_CRITICAL='<EMAIL>'
ALERT_EMAIL_SECURITY='<EMAIL>'
ALERT_EMAIL_WARNING='<EMAIL>'
ALERT_EMAIL_INFO='<EMAIL>'

# Google OAuth Configuration
GOOGLE_CLIENT_ID='your-google-client-id.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET='your-google-client-secret'
GOOGLE_OAUTH_CALLBACK_URL='https://yourdomain.com/admin/'

# Grafana Configuration
GF_SECURITY_ADMIN_PASSWORD='your-secure-grafana-admin-password'
GF_SECURITY_SECRET_KEY='your-grafana-secret-key-32-chars'
GF_SERVER_DOMAIN='grafana.yourdomain.com'
GF_SERVER_ROOT_URL='https://grafana.yourdomain.com'

# Third-party API Keys
SU_API_KEY='your-su-api-key'
SU_API_APP_ID='your-su-api-app-id'
SU_API_BASE_URL='https://connect-sandbox.su-api.com/SUAPI/jservice'

DIGITHERA_BASE_URL="https://www.digithera.it"
DIGITHERA_API_TOKEN="your-digithera-token"
DIGITHERA_IDENTIFIER="<EMAIL>"
VIKEY_API_KEY="your-vikey-api-key"
IPINFO_TOKEN='your-ipinfo-token'

# Company Information
COMPANY_NAME="Your Company Name"
COMPANY_VAT_CODE="your-vat-code"
LOGO_URL="https://cdn.yourdomain.com/logo.jpg"
