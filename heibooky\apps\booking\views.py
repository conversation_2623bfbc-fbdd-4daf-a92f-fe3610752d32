from rest_framework.views import APIView, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.serializers import ValidationError
from rest_framework import status, viewsets
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404, render
from apps.booking.models import Booking, BookingBlock
from apps.booking.serializers import BookingSerializer, BookingBlockSerializer, BookingCancellationRequestSerializer
from django.utils.dateparse import parse_date
from apps.stay.models import Property, Room
import logging
from django.db import IntegrityError, models
import uuid
from django.utils import timezone
from rest_framework.pagination import PageNumberPagination
from apps.users.permissions import BookingPermission
from services.email import EmailService
from services.notification.handlers import GeneralNotificationHandler
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from django.http import HttpResponse
from django.views.generic import TemplateView
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import io
import json
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from apps.booking.api import process_push_reservation

class BookingCreateAPIView(APIView):
    permission_classes = [IsAuthenticated, BookingPermission]

    def post(self, request):
        try:
            serializer = BookingSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            booking_instance = serializer.save()

            # Send notification using GeneralNotificationHandler
            handler = GeneralNotificationHandler(
                users=request.user,
                title="Prenotazione manuale creata con successo",
                message=(
                    f"Una nuova prenotazione manuale è stata aggiunta per la struttura "
                    f"'{booking_instance.property.name}'. Grazie per aver utilizzato Heibooky!"
                )
            )
            handler.send_notification()

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            return Response(
                {"error": "Validation error", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except IntegrityError as e:
            return Response(
                {"error": "Database integrity error", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class PropertyBookingsAPIView(APIView):
    """View to retrieve bookings for a specific property within a specific time."""
    permission_classes = [IsAuthenticated]

    def get(self, request, property_id):
        # Validate UUID format
        try:
            property_uuid = uuid.UUID(str(property_id))
        except (ValueError, TypeError, AttributeError):
            return Response(
                {"error": "Invalid property ID format. Please provide a valid UUID."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get and validate query parameters
            start_date = parse_date(request.query_params.get('start_date'))
            end_date = parse_date(request.query_params.get('end_date'))

            # Validate that dates are provided
            if not start_date or not end_date:
                return Response(
                    {"error": "Both start_date and end_date are required in the format (yyyy-mm-dd)."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if property exists
            if not Property.objects.filter(id=property_uuid).exists():
                return Response(
                    {"error": "Property not found."},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Filter bookings by property and overlapping date range
            bookings = Booking.objects.filter(
                property_id=property_uuid,
                checkin_date__lte=end_date,  # Check-ins start before or on end_date
                checkout_date__gte=start_date  # Check-outs end after or on start_date
            )

            # Serialize the bookings data with request in context
            serializer = BookingSerializer(bookings, many=True, context={'request': request})
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class BookingPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class PropertyBookingListAPIView(APIView):
    """View to retrieve all bookings for properties where user is staff."""
    permission_classes = [IsAuthenticated]
    pagination_class = BookingPagination

    def get(self, request):
        # Get bookings for properties where user is staff
        bookings = Booking.objects.filter(
            property__staffs=request.user
        ).order_by('-booking_date')  # Most recent first

        # Initialize paginator
        paginator = self.pagination_class()
        paginated_bookings = paginator.paginate_queryset(bookings, request)

        # Serialize the paginated data with request in context
        serializer = BookingSerializer(paginated_bookings, many=True, context={'request': request})

        return paginator.get_paginated_response(serializer.data)

class BookingBlockViewSet(viewsets.ModelViewSet):
    queryset = BookingBlock.objects.all()
    serializer_class = BookingBlockSerializer
    permission_classes = [IsAuthenticated, BookingPermission]

    def get_queryset(self):
        """
        Limit the queryset to booking blocks associated with properties
        where the user is a staff member. Additionally, filter by a specific
        property if a 'property' query parameter is provided.
        """
        user = self.request.user
        queryset = BookingBlock.objects.filter(property__staffs=user)

        # Check for 'property' query parameter to filter by specific property
        property_id = self.request.query_params.get("property")
        if property_id:
            try:
                # Validate UUID format
                property_uuid = uuid.UUID(property_id)
                # Check if the property exists and user is a staff member
                property_obj = get_object_or_404(Property, id=property_uuid, staffs=user)
                queryset = queryset.filter(property=property_obj)
            except (ValueError, TypeError):
                raise ValidationError({
                    "property": "Invalid UUID format. Please provide a valid property ID."
                })
            except Property.DoesNotExist:
                raise ValidationError({
                    "property": "Property not found or you don't have permission to access it."
                })
        return queryset

    def perform_create(self, serializer):
        """
        Ensure no overlap with existing blocks when creating a new block.
        Validate dates, check for existing bookings, and cancel if allowed.
        """
        start_date = serializer.validated_data.get("start_date")
        end_date = serializer.validated_data.get("end_date")
        property = serializer.validated_data.get("property")

        try:
            # Date validations
            if start_date < timezone.now().date():
                raise ValidationError({
                    "start_date": "The start date must be today or a future date."
                })
            if end_date <= start_date:
                raise ValidationError({
                    "end_date": "The ending date must follow the start date."
                })

            # Check if the user is a staff member of the property
            if not property.staffs.filter(id=self.request.user.id).exists():
                raise ValidationError({
                    "property": "You do not have permission to create blocks for this property."
                })

            serializer.save()

            # Send notification using GeneralNotificationHandler
            handler = GeneralNotificationHandler(
                users=self.request.user,
                title="Blocco prenotazioni creato con successo",
                message=(
                    f"È stato creato un blocco per la struttura '{property.name}' "
                    f"dal {start_date.strftime('%d/%m/%Y')} al {end_date.strftime('%d/%m/%Y')}. "
                    f"Durante questo periodo, la struttura non sarà prenotabile."
                )
            )
            handler.send_notification()

        except ValidationError as e:
            raise ValidationError({
                "error": str(e)
            })
        except Exception as e:
            raise ValidationError({
                "error": "An unexpected error occurred: " + str(e)
            })

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Endpoint to deactivate a booking block.
        If the block is active (current date is within start_date and end_date),
        it will reactivate all rooms associated with the property.
        """
        try:
            booking_block = self.get_object()

            # Check if the user has permission to deactivate this block
            if not booking_block.property.staffs.filter(id=request.user.id).exists():
                return Response(
                    {"error": "You do not have permission to deactivate this booking block."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Check if the block is active
            if booking_block.is_active:
                # Store property and dates before deleting
                property_id = str(booking_block.property.id)
                start_date = booking_block.start_date
                end_date = booking_block.end_date
                property_name = booking_block.property.name

                # Delete the booking block
                booking_block.delete()

                # Create and send notification using GeneralNotificationHandler
                handler = GeneralNotificationHandler(
                    users=request.user,
                    title="Blocco prenotazioni disattivato con successo",
                    message=(
                        f"Il blocco per la struttura '{property_name}' "
                        f"dal {start_date.strftime('%d/%m/%Y')} al {end_date.strftime('%d/%m/%Y')} "
                        f"è stato disattivato con successo."
                    )
                )
                
                # Send notifications through websocket and database
                handler.send_notification()

                return Response(
                    {
                        "message": "Booking block deactivation process started successfully.",
                        "property_id": property_id
                    },
                    status=status.HTTP_200_OK
                )
            else:
                # Delete the inactive block
                booking_block.delete()

                return Response(
                    {"message": "Inactive booking block removed successfully."},
                    status=status.HTTP_200_OK
                )

        except Exception as e:
            return Response(
                {"error": f"An error occurred while deactivating the booking block: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

class BookingCancellationRequestView(APIView):
    """
    API view for handling booking cancellation requests from users.

    This endpoint allows users to request cancellation of their bookings.
    It validates the booking ID, sends an email notification to the admin,
    and returns a success response.
    """
    permission_classes = [IsAuthenticated, BookingPermission]

    def post(self, request):
        """
        Handle POST requests for booking cancellation.

        For manual bookings (is_manual=True), the booking is automatically cancelled.
        For non-manual bookings, a cancellation request is sent to admin.

        Args:
            request: The HTTP request object containing booking_id and reason

        Returns:
            Response: HTTP response with success message or error details
        """
        try:
            # Initialize serializer with request data and context
            serializer = BookingCancellationRequestSerializer(
                data=request.data,
                context={'request': request}
            )

            # Validate the request data
            if not serializer.is_valid():
                return Response(
                    {'errors': serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get validated data
            booking_id = serializer.validated_data['booking_id']
            reason = serializer.validated_data['reason']

            # Get the booking from context (set during validation)
            booking = serializer.context.get('booking')

            # Handle manual bookings automatically
            if booking.is_manual:
                booking.status = Booking.Status.CANCELLED
                booking.save(update_fields=['status'])
                
                # Send notification using GeneralNotificationHandler
                handler = GeneralNotificationHandler(
                    users=request.user,
                    title="Prenotazione Manuale Cancellata",
                    message=f"La prenotazione manuale per {booking.property.name} è stata cancellata con successo."
                )
                handler.send_notification()
                
                return Response(
                    {
                        'message': 'Manual booking cancelled successfully.',
                        'booking_id': str(booking_id)
                    },
                    status=status.HTTP_200_OK
                )

            # Send email notification to admin
            email_service = EmailService()
            email_sent = email_service.send_booking_cancellation_request(
                user=request.user,
                booking=booking,
                reason=reason
            )

            if not email_sent:
                # Log the error but don't fail the request
                logger = logging.getLogger(__name__)
                logger.error(f"Failed to send cancellation request email for booking {booking_id}")

            # Send notification using GeneralNotificationHandler
            handler = GeneralNotificationHandler(
                users=request.user,
                title="Richiesta di Cancellazione Inviata",
                message=f"La richiesta di cancellazione per la prenotazione è stata inviata. Il nostro team la esaminerà a breve."
            )
            handler.send_notification()

            # Return success response
            return Response(
                {
                    'message': 'Cancellation request submitted successfully. Our team will review your request and contact you soon.',
                    'booking_id': str(booking_id)
                },
                status=status.HTTP_200_OK
            )

        except ValidationError as e:
            return Response(
                {'errors': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Log the unexpected error
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error in booking cancellation request: {str(e)}", exc_info=True)

            return Response(
                {'errors': 'An unexpected error occurred. Please try again later.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class BookingExportView(APIView):
    """
    API view to export bookings to Excel.

    This view generates an Excel file with booking details and returns it as a response.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Handle GET requests to generate and download the Excel file.

        The file includes booking details such as ID, property name, user name, dates, and status.
        """
        # Get all bookings for the authenticated user
        bookings = Booking.objects.filter(property__staffs=request.user).select_related('property', 'user')

        # Create a workbook and add a worksheet
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Bookings"

        # Define the header row
        headers = [
            "ID",
            "Property Name",
            "User Name",
            "Check-in Date",
            "Check-out Date",
            "Status",
            "Total Price"
        ]

        # Add the header row to the worksheet
        worksheet.append(headers)

        # Apply header styles
        for col in range(1, len(headers) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # Add booking data to the worksheet
        for booking in bookings:
            worksheet.append([
                str(booking.id),
                booking.property.name,
                f"{booking.user.first_name} {booking.user.last_name}",
                booking.checkin_date.strftime("%Y-%m-%d"),
                booking.checkout_date.strftime("%Y-%m-%d"),
                booking.status,
                booking.total_price
            ])

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column = [cell for cell in column]
            max_length = max(len(str(cell.value)) for cell in column)
            adjusted_width = (max_length + 2)
            worksheet.column_dimensions[get_column_letter(column[0].column)].width = adjusted_width

        # Generate the Excel file in memory
        excel_file = io.BytesIO()
        workbook.save(excel_file)
        excel_file.seek(0)

        # Create the response with the Excel file
        response = HttpResponse(
            excel_file,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        response['Content-Disposition'] = 'attachment; filename="bookings.xlsx"'
        return response

class BookingExportView(TemplateView):
    """
    View for exporting booking data to Excel for a selected date range
    """
    template_name = 'booking_export.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context
    
    def post(self, request, *args, **kwargs):
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        
        # Validate dates
        try:
            start_date = parse_date(start_date)
            end_date = parse_date(end_date)
            
            if not start_date or not end_date:
                return render(request, self.template_name, {'error': 'Formato data non valido.'})
                
            if start_date > end_date:
                return render(request, self.template_name, {'error': 'La data di inizio deve essere precedente alla data di fine.'})
        except Exception as e:
            return render(request, self.template_name, {'error': f'Errore nella validazione delle date: {str(e)}'})
        
        # Generate Excel file
        try:
            download_url = self.generate_excel_report(start_date, end_date)
            return render(request, self.template_name, {'download_url': download_url})
        except ValueError as e:
            # Handle specific value errors (like no bookings found)
            logger = logging.getLogger(__name__)
            logger.warning(f"Value error generating Excel report: {str(e)}")
            return render(request, self.template_name, {'error': f'Non sono state trovate prenotazioni nel periodo selezionato.'})
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating Excel report: {str(e)}", exc_info=True)
            return render(request, self.template_name, {'error': 'Si è verificato un errore durante la generazione del report.'})
    
    def generate_excel_report(self, start_date, end_date):        # Query bookings within the date range
        # We want bookings that overlap with the selected date range
        bookings = Booking.objects.filter(
            (
                # Check-in within range
                (models.Q(checkin_date__gte=start_date) & models.Q(checkin_date__lte=end_date)) |
                # Check-out within range
                (models.Q(checkout_date__gte=start_date) & models.Q(checkout_date__lte=end_date)) |
                # Stay overlapping with range (check-in before range, check-out after range)
                (models.Q(checkin_date__lt=start_date) & models.Q(checkout_date__gt=end_date))
            )
        ).select_related('property', 'customer', 'reservation_data').order_by('property__name', 'checkin_date')
          # Check if there are any bookings
        if not bookings.exists():
            raise ValueError(f"Nessuna prenotazione trovata tra {start_date} e {end_date}")
          # Create workbook
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Report Prenotazioni"        # Define headers in Italian
        headers = [
            "Nome Proprietà", "ID Proprietà", "Nome Ospite", "Email", "Telefono",
            "Data Check-in", "Data Check-out", "Data Prenotazione", "Stato",
            "Canale", "Prenotazione Manuale", "Prezzo Lordo", "Prezzo Totale", 
            "Deposito", "Tasse Totali", "Commissione", "Tipo Pagamento",
            "Adulti", "Bambini", "Neonati", "Totale Ospiti", "Note"
        ]
        
        # Apply header styles
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Add headers to worksheet
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
        
        # Freeze header row
        worksheet.freeze_panes = 'A2'
        
        # Add data rows
        row_num = 2
        for booking in bookings:
            reservation = booking.reservation_data
            customer = booking.customer
              # Map channel code to readable name
            channel_name = "Unknown"
            if booking.channel_code is not None:
                channel_name = dict(booking.ChannelChoices.choices).get(booking.channel_code, "Unknown")
            
            # Get booking status display name safely
            try:
                status_display = dict(booking.Status.choices).get(booking.status, str(booking.status))
            except Exception:
                status_display = str(booking.status) if booking.status else ""
            row_data = [
                str(booking.property.name) if booking.property.name else "",
                str(booking.property.hotel_id) if booking.property.hotel_id else "",
                str(customer.get_full_name()) if customer else "",
                str(customer.email) if customer and customer.email else "",
                str(customer.telephone) if customer and customer.telephone else "",
                booking.checkin_date.strftime("%Y-%m-%d") if booking.checkin_date else "",
                booking.checkout_date.strftime("%Y-%m-%d") if booking.checkout_date else "",
                booking.booking_date.strftime("%Y-%m-%d %H:%M") if booking.booking_date else "",
                str(status_display),
                str(channel_name),
                "Sì" if booking.is_manual else "No",  # Manual booking indicator
                float(reservation.gross_price) if reservation and reservation.gross_price else 0.00,  # Gross price
                float(reservation.total_price) if reservation and reservation.total_price else 0.00,  # Total price
                float(reservation.deposit) if reservation and reservation.deposit else 0.00,  # Deposit
                float(reservation.total_tax) if reservation and reservation.total_tax else 0.00,  # Total taxes
                float(reservation.commission_amount) if reservation and reservation.commission_amount else 0.00,  # Commission
                str(reservation.payment_type) if reservation and reservation.payment_type else "",  # Payment type
                int(reservation.number_of_adults) if reservation and reservation.number_of_adults is not None else 0,
                int(reservation.number_of_children) if reservation and reservation.number_of_children is not None else 0,
                int(reservation.number_of_infants) if reservation and reservation.number_of_infants is not None else 0,
                int(reservation.number_of_guests) if reservation and reservation.number_of_guests is not None else 0,
                str(reservation.remarks) if reservation and reservation.remarks else ""
            ]
            
            for col_num, cell_value in enumerate(row_data, 1):
                try:
                    worksheet.cell(row=row_num, column=col_num).value = cell_value
                except ValueError as e:
                    # Handle value errors by converting problematic values to string
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Value error for {headers[col_num-1]}: {cell_value} - {str(e)}")
                    try:
                        # Last resort - convert to string and escape problematic characters
                        safe_value = str(cell_value).encode('unicode_escape').decode('utf-8')
                        worksheet.cell(row=row_num, column=col_num).value = safe_value
                    except Exception:
                        # If all else fails, use a placeholder
                        worksheet.cell(row=row_num, column=col_num).value = "[data error]"
            
            row_num += 1
          # Auto-adjust column widths
        for column in worksheet.columns:
            try:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                
                for cell in column:
                    if cell.value:
                        try:
                            cell_length = len(str(cell.value))
                            max_length = max(max_length, cell_length)
                        except Exception:
                            # If we can't get length, use default
                            pass
                
                # Add a little extra space and set a reasonable default
                adjusted_width = min(max(max_length + 2, 10), 50)  # Min 10, max 50 characters
                worksheet.column_dimensions[column_letter].width = adjusted_width
            except Exception as e:
                # Log and continue if there's any error with a specific column
                logger = logging.getLogger(__name__)
                logger.warning(f"Error adjusting column width: {str(e)}")
                continue
          # Create filename with date range
        filename = f"bookings_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.xlsx"
        file_path = f"booking_reports/{filename}"
        
        # Save to BytesIO for upload to S3
        excel_data = io.BytesIO()
        workbook.save(excel_data)
        excel_data.seek(0)
        
        # Ensure booking_reports directory exists in S3
        try:
            # Save to default storage (S3 if configured)
            file_path = default_storage.save(file_path, ContentFile(excel_data.getvalue()))
            
            # Generate download URL
            download_url = default_storage.url(file_path)
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Error saving file to S3: {str(e)}", exc_info=True)
            raise
        
        return download_url


# Initialize logger for webhook
webhook_logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class SuReservationWebhookView(APIView):
    """
    Webhook endpoint to receive push notifications from SU Channel Manager.

    This endpoint handles incoming reservation data pushed by SU's Channel Manager
    following their Push API method. It processes individual reservations and
    sends acknowledgment responses as required.
    """
    permission_classes = []  # No authentication required for webhook

    def post(self, request, *args, **kwargs):
        """
        Handle incoming reservation push notifications from SU Channel Manager.

        According to SU documentation:
        - Su pushes one booking at a time to this endpoint
        - We must acknowledge the reservation notification upon successful processing
        - If acknowledgment is not received, Su will re-push at 10 min intervals
        - After 3 failed attempts, Su will send email notification
        """
        webhook_logger.info("Received reservation push notification from SU Channel Manager")

        try:
            # Parse JSON payload
            if not request.body:
                webhook_logger.error("Empty request body received")
                return Response(
                    {"status": "error", "message": "Empty request body"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                payload = json.loads(request.body)
            except json.JSONDecodeError as e:
                webhook_logger.error(f"Invalid JSON payload: {str(e)}")
                return Response(
                    {"status": "error", "message": "Invalid JSON payload"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            reservations = payload.get("reservations", [])

            # Process the reservation
            result = process_push_reservation(reservations)
            if result['status'] == 'error':
                webhook_logger.error(f"Failed to process reservation: {result['message']}")
                return Response(
                    {"status": "error", "message": result["message"]},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Return response in the exact format required by SU Channel Manager
            notif_id = reservations[0]["reservation_notif_id"]
            return Response(
                {
                    "reservation_notif": {
                        "reservation_notif_id": [
                            notif_id
                        ]
                    }
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            webhook_logger.error(f"Unexpected error processing reservation webhook: {str(e)}", exc_info=True)
            return Response(
                {"status": "error", "message": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


from apps.integrations.tasks import onboarding_task
from apps.stay.models import Property
from services.su_api import delete_property

class BookingTestAPIView(APIView):
    """
    Test API view for booking-related operations.
    This is a placeholder for testing purposes and should be removed in production.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # property = Property.objects.filter().first()  # Get the first property for testing
        # response_data = onboarding_task(property.id, request.user.id)
        response_data = delete_property("99e03695")
        return Response(
            {"response": response_data},
            status=status.HTTP_200_OK
        )