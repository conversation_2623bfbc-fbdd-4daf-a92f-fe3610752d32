import logging
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from botocore.exceptions import ClientError, BotoCoreError
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler for CDN and storage operations
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Handle specific storage and CDN exceptions
    if response is None:
        if isinstance(exc, (ClientError, BotoCoreError)):
            logger.error(f"AWS/S3 error in {context.get('view', 'unknown view')}: {str(exc)}")
            
            # Determine the specific error type
            if isinstance(exc, ClientError):
                error_code = exc.response.get('Error', {}).get('Code', 'Unknown')
                error_message = exc.response.get('Error', {}).get('Message', str(exc))
                
                if error_code in ['NoSuchBucket', 'InvalidBucketName']:
                    custom_response_data = {
                        'error': 'Storage configuration error',
                        'detail': 'There is a problem with the storage configuration. Please try again later.',
                        'code': 'STORAGE_CONFIG_ERROR'
                    }
                    return Response(custom_response_data, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
                elif error_code in ['AccessDenied', 'InvalidAccessKeyId']:
                    custom_response_data = {
                        'error': 'Storage access error',
                        'detail': 'Unable to access storage service. Please try again later.',
                        'code': 'STORAGE_ACCESS_ERROR'
                    }
                    return Response(custom_response_data, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
                elif error_code in ['EntityTooLarge', 'InvalidRequest']:
                    custom_response_data = {
                        'error': 'File upload error',
                        'detail': 'The uploaded file is too large or invalid. Please check file size and format.',
                        'code': 'FILE_UPLOAD_ERROR'
                    }
                    return Response(custom_response_data, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
                
                else:
                    custom_response_data = {
                        'error': 'Storage service error',
                        'detail': 'There was a problem with the storage service. Please try again later.',
                        'code': 'STORAGE_SERVICE_ERROR'
                    }
                    return Response(custom_response_data, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            else:
                # Generic BotoCoreError
                custom_response_data = {
                    'error': 'CDN service error',
                    'detail': 'There was a problem with the CDN service. Please try again later.',
                    'code': 'CDN_SERVICE_ERROR'
                }
                return Response(custom_response_data, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        elif isinstance(exc, ValidationError):
            # Handle Django validation errors
            if hasattr(exc, 'message_dict'):
                custom_response_data = {
                    'error': 'Validation error',
                    'detail': exc.message_dict,
                    'code': 'VALIDATION_ERROR'
                }
            else:
                custom_response_data = {
                    'error': 'Validation error',
                    'detail': exc.messages if hasattr(exc, 'messages') else [str(exc)],
                    'code': 'VALIDATION_ERROR'
                }
            return Response(custom_response_data, status=status.HTTP_400_BAD_REQUEST)
    
    # If we have a response, enhance it with additional context for storage errors
    if response is not None and hasattr(exc, '__class__'):
        exc_class_name = exc.__class__.__name__
        
        # Add storage context to existing responses
        if 'Storage' in str(exc) or 'CDN' in str(exc) or 'S3' in str(exc):
            if isinstance(response.data, dict):
                response.data['storage_error'] = True
                response.data['retry_recommended'] = True
    
    return response


class StorageServiceUnavailable(Exception):
    """Custom exception for when storage service is unavailable"""
    pass


class CDNServiceUnavailable(Exception):
    """Custom exception for when CDN service is unavailable"""
    pass


class FileOptimizationError(Exception):
    """Custom exception for file optimization errors"""
    pass
