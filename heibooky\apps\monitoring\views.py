import json
import logging
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from .metrics import get_health_status, metrics_collector

logger = logging.getLogger(__name__)


class HealthCheckView(View):
    """Health check endpoint for load balancers and monitoring."""
    
    def get(self, request):
        """Return health status."""
        health = get_health_status()
        
        # Determine HTTP status based on health
        if health['status'] == 'healthy':
            status_code = 200
        elif health['status'] == 'degraded':
            status_code = 200  # Still serving requests
        else:
            status_code = 503  # Service unavailable
            
        return JsonResponse(health, status=status_code)


class ReadinessCheckView(View):
    """Readiness check for Kubernetes deployments."""
    
    def get(self, request):
        """Return readiness status."""
        try:
            # Check if application is ready to serve requests
            health = get_health_status()
            
            # For readiness, we're stricter - require database and cache
            critical_checks = ['database', 'cache']
            is_ready = all(
                health['checks'].get(check, '').startswith('healthy')
                for check in critical_checks
            )
            
            if is_ready:
                return JsonResponse({'status': 'ready'}, status=200)
            else:
                return JsonResponse({
                    'status': 'not ready',
                    'checks': health['checks']
                }, status=503)
                
        except Exception as e:
            logger.error(f"Readiness check failed: {e}")
            return JsonResponse({
                'status': 'error',
                'error': str(e)
            }, status=503)


class LivenessCheckView(View):
    """Liveness check for Kubernetes deployments."""
    
    def get(self, request):
        """Return liveness status."""
        # Simple liveness check - if we can respond, we're alive
        return JsonResponse({'status': 'alive'}, status=200)


class MetricsView(View):
    """Prometheus metrics endpoint."""
    
    def get(self, request):
        """Return Prometheus metrics."""
        try:
            # Collect latest metrics
            metrics_collector.collect_all_metrics()
            
            # Generate Prometheus format
            metrics_data = generate_latest()
            
            return HttpResponse(
                metrics_data,
                content_type=CONTENT_TYPE_LATEST
            )
        except Exception as e:
            logger.error(f"Error generating metrics: {e}")
            return HttpResponse(
                "# Error generating metrics\n",
                content_type=CONTENT_TYPE_LATEST,
                status=500
            )


@method_decorator(csrf_exempt, name='dispatch')
class AlertWebhookView(View):
    """Webhook endpoint for receiving alerts from Alertmanager."""
    
    def post(self, request):
        """Handle incoming alerts."""
        try:
            data = json.loads(request.body)
            
            # Log the alert
            for alert in data.get('alerts', []):
                status = alert.get('status', 'unknown')
                alert_name = alert.get('labels', {}).get('alertname', 'unknown')
                summary = alert.get('annotations', {}).get('summary', '')
                
                if status == 'firing':
                    logger.warning(f"Alert FIRING: {alert_name} - {summary}")
                elif status == 'resolved':
                    logger.info(f"Alert RESOLVED: {alert_name} - {summary}")
                
                # Here you could add logic to:
                # - Send notifications to Slack/Discord
                # - Create tickets in your issue tracking system
                # - Send SMS alerts for critical issues
                # - Auto-scale resources if needed
                
            return JsonResponse({'status': 'ok'})
            
        except json.JSONDecodeError:
            logger.error("Invalid JSON in alert webhook")
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"Error processing alert webhook: {e}")
            return JsonResponse({'error': 'Internal error'}, status=500)


class MetricsDebugView(View):
    """Debug view to see current metrics in JSON format."""
    
    def get(self, request):
        """Return current metrics in JSON format for debugging."""
        try:
            metrics_collector.collect_all_metrics()
            
            # This is a simplified view - in production you might want to
            # parse the Prometheus metrics and return them in JSON format
            health = get_health_status()
            
            debug_info = {
                'health': health,
                'metrics_endpoint': '/metrics',
                'prometheus_format': True,
                'collection_status': 'ok'
            }
            
            return JsonResponse(debug_info)
            
        except Exception as e:
            logger.error(f"Error in metrics debug view: {e}")
            return JsonResponse({
                'error': str(e),
                'collection_status': 'error'
            }, status=500)
