"""
ASGI config for heibooky project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/asgi/
"""
import os
from django.core.asgi import get_asgi_application
import logging

logger = logging.getLogger(__name__)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heibooky.settings')
django_asgi_app = get_asgi_application()

# Import after Django apps are loaded
from channels.routing import ProtocolTypeRouter, URLRouter
from apps.integrations.routing import websocket_urlpatterns
from apps.integrations.middleware import TokenAuthMiddleware
from services.notification import AllowedOriginsValidator

# Log the CORS settings for debugging
from django.conf import settings
logger.info(f"CORS_ALLOWED_ORIGINS: {getattr(settings, 'CORS_ALLOWED_ORIGINS', [])}")

# Conditional CORS validation based on DEBUG setting
if settings.DEBUG:
    # In development, skip CORS validation for easier testing
    logger.info("DEBUG mode: Skipping CORS validation for WebSocket connections")
    websocket_app = TokenAuthMiddleware(URLRouter(websocket_urlpatterns))
else:
    # In production, enforce CORS validation
    logger.info("Production mode: Enforcing CORS validation for WebSocket connections")
    websocket_app = AllowedOriginsValidator(
        TokenAuthMiddleware(URLRouter(websocket_urlpatterns))
    )

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": websocket_app,
})
