from celery import shared_task
from services.email import PropertyEmailService
import logging
from django.db import transaction
from django.utils import timezone

logger = logging.getLogger(__name__)


@shared_task
def send_invitation_email(invite_id):
    """
    Task to send invitation email.

    Args:
        invite_id: UUID of the TeamInvite instance
    """
    from apps.stay.models import TeamInvite  # Import here to avoid circular imports

    try:
        invite = TeamInvite.objects.get(id=invite_id)
        email_service = PropertyEmailService()
        success = email_service.send_property_invitation(invite)

        if not success:
            invite.delete()

        return success
    except TeamInvite.DoesNotExist:
        logger.error(f"Team invite {invite_id} not found")
        return False
    except Exception as e:
        logger.error(f"Failed to send invitation email: {str(e)}")
        return False

@shared_task
def process_property_cover_image(property_id, photo_id=None, action="create"):
    """
    Process a property's cover image based on its photos.

    This task handles:
    1. Setting the first uploaded photo as the cover image
    2. Updating the cover image when the first photo is deleted
    3. Removing the cover image when all photos are deleted

    Args:
        property_id: UUID of the property
        photo_id: UUID of the photo that triggered this task (optional)
        action: Action that triggered this task ("create", "delete")
    """
    from apps.stay.models import Property, Photo
    from apps.stay.utils.image_utils import generate_cover_image

    try:
        # Get the property
        property_instance = Property.objects.get(id=property_id)

        # Get all photos for this property, ordered by creation date
        property_photos = Photo.objects.filter(
            property=property_instance
        ).order_by('created_at')

        # If there are no photos, remove the cover image
        if not property_photos.exists():
            if property_instance.cover_image:
                logger.info(f"Removing cover image for property {property_id} as it has no photos")

                # Get the old image name for reference
                old_image_name = property_instance.cover_image.name

                # Clear the cover image - this will handle file deletion through the storage backend
                property_instance.cover_image = None
                property_instance.save(update_fields=['cover_image'])

                logger.info(f"Removed cover image {old_image_name} for property {property_id}")
            return True

        # Get the first photo
        first_photo = property_photos.first()

        # If this is a create action or the property has no cover image, set the cover image
        if action == "create" or not property_instance.cover_image:
            logger.info(f"Setting cover image for property {property_id} using photo {first_photo.id}")

            # Generate a compressed version of the image
            with transaction.atomic():
                # Open the image file
                first_photo.image.open()

                # Generate a compressed version
                compressed_image = generate_cover_image(first_photo.image)

                if compressed_image:
                    # Set the cover image
                    property_instance.cover_image = compressed_image
                    property_instance.save(update_fields=['cover_image'])
                    logger.info(f"Successfully set cover image for property {property_id}")
                else:
                    logger.error(f"Failed to generate cover image for property {property_id}")

                # Close the image file
                first_photo.image.close()

        # If this is a delete action and the deleted photo was used as the cover image,
        # update the cover image to use the new first photo
        elif action == "delete" and photo_id:
            # We can't directly check if the deleted photo was the cover image
            # since it's already deleted, but we can update the cover image anyway
            logger.info(f"Updating cover image for property {property_id} after photo deletion")

            # Generate a compressed version of the new first photo
            with transaction.atomic():
                # Open the image file
                first_photo.image.open()

                # Generate a compressed version
                compressed_image = generate_cover_image(first_photo.image)

                if compressed_image:
                    # Get the old image name for reference
                    old_image_name = property_instance.cover_image.name if property_instance.cover_image else None

                    # Set the cover image - Django will handle the old file deletion automatically
                    property_instance.cover_image = compressed_image
                    property_instance.save(update_fields=['cover_image'])

                    if old_image_name:
                        logger.info(f"Successfully updated cover image for property {property_id} (replaced {old_image_name})")
                    else:
                        logger.info(f"Successfully updated cover image for property {property_id}")
                else:
                    logger.error(f"Failed to generate cover image for property {property_id}")

                # Close the image file
                first_photo.image.close()

        return True

    except Property.DoesNotExist:
        logger.error(f"Property {property_id} not found")
        return False
    except Exception as e:
        logger.error(f"Error processing cover image for property {property_id}: {str(e)}", exc_info=True)
        return False

@shared_task
def send_property_setup_reminders():
    """
    Send reminder emails to users with incomplete property setups.

    Reminder schedule:
    - First reminder: 24 hours after property creation
    - Second reminder: 3 days after creation
    - Final reminder: 7 days after creation

    After the third reminder, no more emails will be sent.
    """
    from apps.stay.models import Property, PropertyMetadata
    from apps.stay.utils.property_setup import check_property_setup_status

    today = timezone.now()
    logger.info("Starting property setup reminder task")

    # Get all properties with metadata that are not marked as complete
    incomplete_properties = Property.objects.filter(
        metadata__is_setup_complete=False
    ).select_related('metadata')

    logger.info(f"Found {incomplete_properties.count()} properties with incomplete setup")

    email_service = PropertyEmailService()
    reminders_sent = 0

    for prop in incomplete_properties:
        try:
            # Skip if property doesn't have metadata
            if not hasattr(prop, 'metadata'):
                logger.warning(f"Property {prop.id} has no metadata, skipping")
                continue

            # Get days since creation
            days_since_creation = (today - prop.created_at).days

            # Skip if already sent 3 reminders
            if prop.metadata.reminder_count >= 3:
                continue

            # Determine if it's time to send a reminder
            should_send = False

            if prop.metadata.reminder_count == 0 and days_since_creation >= 1:
                # First reminder: 24 hours after creation
                should_send = True
            elif prop.metadata.reminder_count == 1 and days_since_creation >= 3:
                # Second reminder: 3 days after creation
                should_send = True
            elif prop.metadata.reminder_count == 2 and days_since_creation >= 7:
                # Final reminder: 7 days after creation
                should_send = True

            if should_send:
                # Check if property is still incomplete
                is_complete, missing_items, status_data = check_property_setup_status(prop)

                if is_complete:
                    # Property is now complete, update metadata
                    prop.metadata.is_setup_complete = True
                    prop.metadata.save(update_fields=['is_setup_complete'])
                    logger.info(f"Property {prop.id} is now complete, no reminder needed")
                    continue

                # Log detailed status information for debugging
                logger.debug(f"Property {prop.id} setup status: {status_data}")

                # Get the first staff member (owner)
                user = prop.staffs.first()
                if not user:
                    logger.warning(f"Property {prop.id} has no staff members, skipping reminder")
                    continue

                # Send reminder email
                success = email_service.send_setup_reminder(user, prop, missing_items)

                if success:
                    # Update reminder metadata
                    prop.metadata.reminder_count += 1
                    prop.metadata.last_reminder_sent = today
                    prop.metadata.save(update_fields=['reminder_count', 'last_reminder_sent'])
                    reminders_sent += 1

        except Exception as e:
            logger.error(f"Error processing reminder for property {prop.id}: {str(e)}", exc_info=True)

    logger.info(f"Property setup reminder task completed. Sent {reminders_sent} reminders.")
    return reminders_sent
