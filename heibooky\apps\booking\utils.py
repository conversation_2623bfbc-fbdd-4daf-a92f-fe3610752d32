import string
import secrets
import urllib.parse

def generate_unique_id(model_name, length=10, max_retries=10):
    """Generates a unique ID for reservation model during manual booking field of BookingBlock."""
    for attempt in range(max_retries):
        result_str = ''.join(secrets.choice(string.ascii_letters + string.digits) for i in range(length))
        if not model_name.objects.filter(id=result_str).exists():
            return result_str
    raise ValueError("Failed to generate unique ID after multiple attempts")


def get_whatsapp_link(phone_number: str) -> str:
    """
    Generate a WhatsApp link with a localized message based on phone number prefix.
    
    Args:
        phone_number (str): The phone number to create WhatsApp link for
        
    Returns:
        str: WhatsApp link with encoded message
    """
    if not phone_number:
        return ""
    
    # Remove symbols and spaces, keep only digits
    cleaned = ''.join(filter(str.isdigit, phone_number))
    
    if not cleaned:
        return ""
    
    # Remove leading zeros from international format (00xx becomes xx)
    if cleaned.startswith('00'):
        cleaned = cleaned[2:]
    
    # Prefix → Language (simplified map, expandable)
    language_map = {
        '39': 'it',  # Italy
        '33': 'fr',  # France
        '49': 'de',  # Germany
        '44': 'en',  # UK
        '34': 'es',  # Spain
        '1': 'en',   # US/Canada
        '351': 'pt', # Portugal
        '41': 'de',  # Switzerland (German)
        '43': 'de',  # Austria
        '32': 'fr',  # Belgium (French)
    }
    
    # Determine prefix (try 3 digits first, then 2)
    prefix = None
    if len(cleaned) >= 3:
        prefix = cleaned[:3]
        if prefix not in language_map and len(cleaned) >= 2:
            prefix = cleaned[:2]
    elif len(cleaned) >= 2:
        prefix = cleaned[:2]
    
    lang = language_map.get(prefix, 'en')  # Default: English
    
    # Translated Messages
    messages = {
        'it': "Ciao, grazie per la prenotazione. Sono il proprietario di questa struttura. Scambiamoci informazioni utili prima del tuo arrivo",
        'en': "Hello, thank you for booking. I am the owner of this property. Let's exchange useful information before your arrival",
        'fr': "Bonjour, merci pour votre réservation. Je suis le propriétaire de cette propriété. Échangeons des informations utiles avant votre arrivée",
        'de': "Hallo, vielen Dank für Ihre Buchung. Ich bin der Eigentümer dieser Unterkunft. Lassen Sie uns nützliche Informationen vor Ihrer Ankunft austauschen",
        'es': "Hola, gracias por reservar. Soy el propietario de esta propiedad. Intercambiemos información útil antes de tu llegada",
        'pt': "Olá, obrigado pela reserva. Sou o proprietário desta propriedade. Vamos trocar informações úteis antes da sua chegada"
    }
    
    message = messages.get(lang, messages['en'])
    encoded_message = urllib.parse.quote(message)
    
    return f"https://wa.me/{cleaned}?text={encoded_message}"