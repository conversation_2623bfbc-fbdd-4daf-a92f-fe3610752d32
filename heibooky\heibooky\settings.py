from pathlib import Path
import os
import environ
from datetime import timedelta
from celery.schedules import crontab

# Load environment variables from a .env file
env = environ.Env()
environ.Env.read_env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings
SECRET_KEY = env('SECRET_KEY')

DEBUG = env.bool('DEBUG', default=True)

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=['*'])
CORS_ALLOWED_ORIGINS = [
    'http://localhost:8000',
    'http://localhost:3000',
    'http://127.0.0.1:8000',
    'http://127.0.0.1:3000',
    'https://backend.heibooky.com',
    'https://partner.heibooky.com',
    'https://support.heibooky.com',
    'https://www.heibooky.com',
    'https://heibooky.com',
    'https://heibooky-web-app.vercel.app',
]

CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

CORS_ALLOW_CREDENTIALS = True
CORS_PREFLIGHT_MAX_AGE = 86400  # 24 hours

# Application definition
INSTALLED_APPS = [
    # Django default apps
    'daphne',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',

    # Auth apps
    'dj_rest_auth',
    'dj_rest_auth.registration',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',

    # REST framework apps
    'rest_framework',
    'rest_framework.authtoken',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',

    # Local apps
    'apps.office',
    'apps.billing',
    'apps.booking',
    'apps.integrations',
    'apps.monitoring',
    'apps.pricing',
    'apps.reviews',
    'apps.stay',
    'apps.support',
    'apps.users',

    # Third-party apps
    'django_celery_beat',
    'channels',
    'channels_redis',
    'corsheaders',
    'storages',
    'phonenumber_field',
    'services',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'apps.monitoring.middleware.PrometheusMiddleware',
    'apps.monitoring.middleware.MetricsCollectionMiddleware',
    'services.logging.middleware.RequestLogMiddleware',
    'services.logging.middleware.SecurityLogMiddleware',
    'services.logging.middleware.PerformanceLogMiddleware',
]

ROOT_URLCONF = 'heibooky.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'heibooky.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('DB_NAME'),
        'USER': env('DB_USER'),
        'PASSWORD': env('DB_PASSWORD'),
        'HOST': env('DB_HOST'),
        'PORT': env('DB_PORT'),
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 10,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Password Hashers
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.Argon2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher',
    'django.contrib.auth.hashers.BCryptSHA256PasswordHasher',
]

AUTH_USER_MODEL = 'users.User'

# Django AllAuth settings
ACCOUNT_USER_MODEL_USERNAME_FIELD = None
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_ADAPTER = 'apps.users.adapters.CustomAccountAdapter'
ACCOUNT_EMAIL_SUBJECT_PREFIX = 'Heibooky - '
REST_USE_JWT = True
JWT_AUTH_COOKIE = 'auth-token'
JWT_AUTH_REFRESH_COOKIE = 'refresh-token'

REST_AUTH = {
    'USER_DETAILS_SERIALIZER': 'apps.users.serializers.UserSerializer',
    'JWT_AUTH_COOKIE': 'auth-token',
    'JWT_AUTH_REFRESH_COOKIE': 'refresh-token',
    'JWT_AUTH_HTTPONLY': not DEBUG,
    'USE_JWT': True,
    'JWT_AUTH_RETURN_EXPIRATION': True,
    'SESSION_LOGIN': False,
    'TOKEN_MODEL': None,
}

# Authentication backends
AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
)

# Social account settings
SOCIALACCOUNT_ADAPTER = 'apps.users.adapters.CustomSocialAccountAdapter'
SOCIALACCOUNT_EMAIL_VERIFICATION = 'none'
SOCIALACCOUNT_EMAIL_REQUIRED = True
SOCIALACCOUNT_AUTO_SIGNUP = True
SOCIALACCOUNT_QUERY_EMAIL = True
SOCIALACCOUNT_STORE_TOKENS = True

SITE_ID = 1

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': env('GOOGLE_CLIENT_ID'),
            'secret': env('GOOGLE_CLIENT_SECRET'),
            'key': ''
        },
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        },
        'VERIFIED_EMAIL': True,
        'OAUTH_PKCE_ENABLED': True,
    }
}

# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en'

LANGUAGES = [
    ('en', 'English'),
    ('it', 'Italian'),
]

TIME_ZONE = 'Europe/Rome'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Phone number field settings
PHONENUMBER_DEFAULT_REGION = "IT"
PHONENUMBER_DB_FORMAT = "E164"
PHONENUMBER_DEFAULT_FORMAT = "E164"

# Cloud Storage and CDN Configuration
AWS_ACCESS_KEY_ID = env("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = env("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = env("AWS_STORAGE_BUCKET_NAME")
AWS_S3_ENDPOINT_URL = env("AWS_S3_ENDPOINT_URL")
AWS_CDN_URL = env("AWS_CDN_URL")
AWS_LOCATION = "media"
AWS_S3_SIGNATURE_VERSION = "s3v4"
AWS_DEFAULT_ACL = "public-read"

# Media cleanup configuration
# Set to True to enable actual deletion of old media files
# Set to False to only log what would be deleted (safe mode)
ENABLE_MEDIA_FILE_DELETION = env.bool("ENABLE_MEDIA_FILE_DELETION", default=False)

# Enhanced S3 object parameters for optimal CDN caching
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": "max-age=86400",  # 24 hours cache
    "Metadata": {
        "uploaded-by": "heibooky-backend"
    }
}

# Custom Storage Backends with CDN Integration
STORAGES = {
    "default": {
        "BACKEND": "services.storage.storage.OptimizedCDNStorage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "location": AWS_LOCATION,
            "default_acl": AWS_DEFAULT_ACL,
            "object_parameters": AWS_S3_OBJECT_PARAMETERS,
            "custom_domain": AWS_CDN_URL.replace('https://', '').replace('http://', '') if AWS_CDN_URL else None,
        }
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.ManifestStaticFilesStorage" if not DEBUG else "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
    "photo_storage": {
        "BACKEND": "services.storage.storage.PhotoStorage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "location": AWS_LOCATION,
            "default_acl": AWS_DEFAULT_ACL,
            "object_parameters": {
                **AWS_S3_OBJECT_PARAMETERS,
                "ContentType": "image/jpeg",
                "ContentDisposition": "inline",
            },
            "custom_domain": AWS_CDN_URL.replace('https://', '').replace('http://', '') if AWS_CDN_URL else None,
        }
    },
    "document_storage": {
        "BACKEND": "services.storage.storage.DocumentStorage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "location": AWS_LOCATION,
            "default_acl": AWS_DEFAULT_ACL,
            "object_parameters": {
                **AWS_S3_OBJECT_PARAMETERS,
                "ContentType": "application/pdf",
                "ContentDisposition": "attachment",
            },
            "custom_domain": AWS_CDN_URL.replace('https://', '').replace('http://', '') if AWS_CDN_URL else None,
        }
    },
    # Legacy support - will be migrated to document_storage
    "invoice_storage": {
        "BACKEND": "services.storage.storage.DocumentStorage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "location": AWS_LOCATION,
            "default_acl": AWS_DEFAULT_ACL,
            "object_parameters": {
                **AWS_S3_OBJECT_PARAMETERS,
                "ContentType": "application/pdf",
                "ContentDisposition": "attachment",
            },
            "custom_domain": AWS_CDN_URL.replace('https://', '').replace('http://', '') if AWS_CDN_URL else None,
        }
    },
}

# CDN-optimized Media URL Configuration
MEDIA_URL = f"{AWS_CDN_URL}/{AWS_LOCATION}/" if AWS_CDN_URL else f"/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# File Upload Configuration
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 25 * 1024 * 1024  # 25MB
FILE_UPLOAD_PERMISSIONS = 0o644

# Image Processing Configuration
IMAGE_OPTIMIZATION = {
    'ENABLE_ASYNC_PROCESSING': True,
    'MAX_IMAGE_SIZE': (1920, 1920),  # Max dimensions
    'JPEG_QUALITY': 85,
    'ENABLE_EXIF_TRANSPOSE': True,
    'SUPPORTED_FORMATS': ['JPEG', 'PNG', 'GIF', 'WEBP'],
}

# Enhanced Static Files Configuration for Production
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Static files finders
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Production-optimized static files configuration
if not DEBUG:
    # Use ManifestStaticFilesStorage for better caching in production
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'
    # Disable WhiteNoise for static files in production when using CDN
    WHITENOISE_USE_FINDERS = False
    WHITENOISE_AUTOREFRESH = False
    WHITENOISE_MANIFEST_STRICT = False
else:
    # Development settings
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = True
    WHITENOISE_MANIFEST_STRICT = False

# Urls
FRONTEND_URL = env('FRONTEND_URL')
ADMIN_URL = env('ADMIN_URL')
SUPPORT_URL = env('SUPPORT_URL')

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Rest Framework settings
REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly"
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle"
    ],
    "DEFAULT_THROTTLE_RATES": {
        "anon": "100/hour",
        "user": "2400/hour",  # 40 requests per minute
        "user_read": "2400/hour",  # 40 requests per minute
        "user_write": "600/hour"   # 10 requests per minute
    },
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.MultiPartParser",
        "rest_framework.parsers.FormParser",
        "rest_framework.parsers.JSONParser",
    ],
    "UPLOADED_FILES_USE_URL": True,
    "MAX_UPLOAD_SIZE": 25 * 1024 * 1024,  # 25MB
    # Enhanced error handling for CDN operations
    "EXCEPTION_HANDLER": "services.storage.exceptions.custom_exception_handler",
}


# JWT settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=12),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'JTI_CLAIM': 'jti',
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(hours=12),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=7),
}

# Channels Configuration
ASGI_APPLICATION = 'heibooky.asgi.application'

# Redis Configuration for local development (no authentication)
# Redis Connection Settings
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

# Construct Redis URL without authentication for local development
def get_redis_url():
    """Construct Redis URL without authentication"""
    return f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# Default Redis URL
REDIS_URL = get_redis_url()

# Redis Cache Configuration with connection pooling
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 50,
                "retry_on_timeout": True,
                "socket_keepalive": True,
                "socket_keepalive_options": {},
                "health_check_interval": 30,
            },
            "SERIALIZER": "django_redis.serializers.json.JSONSerializer",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "IGNORE_EXCEPTIONS": True,
        },
        "TIMEOUT": 300,  # 5 minutes default timeout
        "VERSION": 1,
    }
}

# Channel Layers Configuration
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer"
    } if DEBUG else {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [REDIS_URL],
            "capacity": 1500,
            "expiry": 60,
        },
    }
}

# Celery Configuration with Redis
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL

# Celery Security and Performance Settings
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True

# Celery Redis Broker Settings
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_BROKER_TRANSPORT_OPTIONS = {
    'visibility_timeout': 3600,
    'fanout_prefix': True,
    'fanout_patterns': True,
    'socket_keepalive': True,
    'health_check_interval': 30,
}

# Celery Redis Result Backend Settings
CELERY_RESULT_BACKEND_TRANSPORT_OPTIONS = {
    'socket_keepalive': True,
    'socket_keepalive_options': {},
    'health_check_interval': 30,
}

# Celery Task Settings
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 300  # 5 minutes
CELERY_TASK_SOFT_TIME_LIMIT = 240  # 4 minutes
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 50
CELERY_BEAT_SCHEDULE = {
    'complete-past-bookings': {
        'task': 'apps.booking.tasks.complete_past_bookings',
        'schedule': crontab(hour=0, minute=30),
        'options': {'expires': 3600},
    },
    'send-booking-reminders': {
        'task': 'apps.booking.tasks.send_booking_reminders',
        'schedule': crontab(hour=8, minute=0),  # Run every day at 8:00 AM
        'options': {'expires': 3600},
    },
    'generate-digithera-invoices': {
        'task': 'apps.integrations.tasks.generate_digithera_invoice_task',
        'schedule': crontab(minute='*/15'),
        'options': {'expires': 900},
    },
    'process-owner-payouts': {
        'task': 'apps.integrations.tasks.process_owner_payouts',
        'schedule': crontab(hour=9, minute=0),
        'options': {'expires': 3600},
    },
    'monitor-stripe-transfers': {
        'task': 'apps.integrations.tasks.monitor_stripe_transfers',
        'schedule': crontab(minute='*/30'),
        'options': {'expires': 1800},
    },
    'delete-expired-room-rates': {
        'task': 'apps.pricing.tasks.delete_expired_room_rates',
        'schedule': crontab(hour=1, minute=0),  # Run daily at 1:00 AM
        'options': {'expires': 3600},
    },
    'send-property-setup-reminders': {
        'task': 'apps.stay.tasks.send_property_setup_reminders',
        'schedule': crontab(hour=9, minute=0),  # Run daily at 9:00 AM
        'options': {'expires': 3600},
    },
    'sync-inventory-empty-dates': {
        'task': 'apps.booking.tasks.sync_inventory_for_empty_dates',
        'schedule': crontab(month_of_year=1, day_of_month=1, hour=2, minute=0),  # Run once a year on January 1st at 2:00 AM
        'options': {'expires': 7200},
    },
    # CDN and Storage monitoring tasks
    'monitor-cdn-health': {
        'task': 'services.storage.tasks.monitor_cdn_health',
        'schedule': crontab(minute='*/10'),  # Run every 10 minutes
        'options': {'expires': 600},
    },
    'cleanup-old-media-files': {
        'task': 'services.storage.tasks.cleanup_old_media_files',
        'schedule': crontab(hour=2, minute=0, day_of_week=0),  # Run weekly on Sunday at 2:00 AM
        'kwargs': {'days_old': 30},
        'options': {'expires': 7200},
    },
}

# Session engine using Redis
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"
SESSION_COOKIE_AGE = 3600  # 1 hour
SESSION_SAVE_EVERY_REQUEST = True

# Email Configuration
EMAIL_BACKEND = 'anymail.backends.mailgun.EmailBackend'
ANYMAIL = {
    "MAILGUN_API_KEY": env('MAILGUN_API_KEY'),
    "MAILGUN_SENDER_DOMAIN": env('MAILGUN_DOMAIN'),  # Using MAILGUN_DOMAIN from env
    "MAILGUN_API_URL": "https://api.eu.mailgun.net/v3",  # Use EU endpoint if your domain is in EU
}
EMAIL_HOST_USER = env('DEFAULT_FROM_EMAIL')
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL')

#Digithera API Configuration
DIGITHERA_BASE_URL = env('DIGITHERA_BASE_URL')
DIGITHERA_IDENTIFIER = env('DIGITHERA_IDENTIFIER')
DIGITHERA_API_TOKEN = env('DIGITHERA_API_TOKEN')

# Company Information
COMPANY_VAT_CODE = env('COMPANY_VAT_CODE')
COMPANY_NAME = env('COMPANY_NAME')

# Stripe Configuration
STRIPE_SECRET_KEY = env('STRIPE_SECRET_KEY')

IPINFO_TOKEN = env('IPINFO_TOKEN')

# SU API API Configuration
SU_API_BASE_URL = env('SU_API_BASE_URL')
SU_API_APP_ID = env('SU_API_APP_ID')
SU_API_KEY = env('SU_API_KEY')

# SU API Retry Settings
SU_API_MAX_RETRIES = 3
SU_API_INITIAL_DELAY = 1
SU_API_MAX_DELAY = 60
SU_API_BACKOFF_FACTOR = 2
SU_API_JITTER = 0.1

# Security settings for production
if not DEBUG:
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    X_FRAME_OPTIONS = 'DENY'
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

# security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
#CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_HTTPONLY = False
CSRF_USE_SESSIONS = False
CSRF_COOKIE_NAME = 'csrftoken'

# Content Security Policy
CONTENT_SECURITY_POLICY = {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.jsdelivr.net", "https://js.stripe.com"],
    'style-src': ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com"],
    'img-src': ["'self'", "data:", "https:", "blob:"],
    'font-src': ["'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net"],
    'connect-src': ["'self'", "https://api.stripe.com", "wss:", "https:"],
    'frame-src': ["'self'", "https://js.stripe.com"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'self'"],
    'upgrade-insecure-requests': True,
}

# Cookie settings
SESSION_COOKIE_SAMESITE = 'Lax'

# Create logs directory if it doesn't exist
LOGS_DIR = '/var/log/heibooky' if not DEBUG else str(BASE_DIR / 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

# Logging Configuration
def ensure_log_directory_exists(log_dir):
    """Ensure the logging directory exists and is writable."""
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
        except Exception as e:
            # Fallback to local directory if we can't create the specified path
            log_dir = os.path.join(BASE_DIR, 'logs')
            os.makedirs(log_dir, exist_ok=True)

    # Ensure log files exist and have the correct permissions
    log_files = ['django.log', 'error.log', 'celery.log']
    for log_file in log_files:
        log_file_path = os.path.join(log_dir, log_file)
        if not os.path.exists(log_file_path):
            with open(log_file_path, 'w') as f:
                f.write('')
            os.chmod(log_file_path, 0o644)  # Set file permissions to -rw-r--r--

    return log_dir

# Determine log directory based on environment
if os.environ.get('DOCKER_CONTAINER'):
    LOG_DIR = '/var/log/heibooky'
else:
    LOG_DIR = os.path.join(BASE_DIR, 'logs')

# Ensure log directory exists
LOG_DIR = ensure_log_directory_exists(LOG_DIR)

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            '()': 'services.logging.formatters.DetailedRequestFormatter',
            'format': '{levelname} [{asctime}] {module} {process:d} {thread:d} {message} '
                      'Source IP: {source_ip} HTTP Method: {http_method} Requested URL: {requested_url} '
                      'User Agent: {user_agent} Request Headers: {request_headers} '
                      'User ID: {user_id} Session ID: {session_id} Server IP: {server_ip} Process ID: {process_id}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'structured': {
            '()': 'services.logging.formatters.StructuredFormatter',
        },
        'simple': {
            'format': '{levelname} [{asctime}] {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'security': {
            '()': 'services.logging.formatters.SecurityFormatter',
            'format': '{levelname} [{asctime}] {message} - Security Context: {security_context}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
        'performance': {
            '()': 'services.logging.formatters.PerformanceFormatter',
            'format': '{levelname} [{asctime}] {message} - Duration: {duration_ms} - Memory: {memory_mb}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'ERROR',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file_django': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'django.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'delay': True,
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'error.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'delay': True,
        },
        'file_celery': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'celery.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'delay': True,
        },
        'file_structured': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'structured.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'structured',
            'delay': True,
        },
        'file_security': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'security.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 10,
            'formatter': 'security',
            'delay': True,
        },
        'file_performance': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'performance.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'performance',
            'delay': True,
        },
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler',
        }
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file_django', 'file_structured', 'mail_admins'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.server': {
            'handlers': ['console', 'file_django', 'file_structured'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console', 'file_error', 'file_structured', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console', 'file_celery', 'file_structured', 'file_error'],
            'level': 'INFO',
            'propagate': True,
        },
        'heibooky': {
            'handlers': ['console', 'file_django', 'file_structured', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'apps.monitoring': {
            'handlers': ['console', 'file_django', 'file_structured'],
            'level': 'INFO',
            'propagate': False,
        },
        'security': {
            'handlers': ['console', 'file_security', 'file_structured', 'mail_admins'],
            'level': 'WARNING',
            'propagate': False,
        },
        'performance': {
            'handlers': ['console', 'file_performance', 'file_structured'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console', 'file_celery', 'file_error'],
            'level': 'INFO',
            'propagate': True,
        },
        'heibooky': {
            'handlers': ['console', 'file_django', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
    },
}

# Update email settings for error notifications
ADMINS = [('Admin', env('ADMIN_EMAIL'))] if not DEBUG else []
SERVER_EMAIL = DEFAULT_FROM_EMAIL

# Redis connection validation function
def validate_redis_connection():
    """Validate Redis connection on startup"""
    try:
        from django.core.cache import cache
        cache.set('health_check', 'ok', timeout=10)
        result = cache.get('health_check')
        if result != 'ok':
            raise Exception("Redis cache validation failed")
        cache.delete('health_check')
        return True
    except Exception as e:
        print(f"Redis connection validation failed: {e}")
        return False

# Optional: Add Redis connection monitoring
REDIS_CONNECTION_POOL_KWARGS = {
    'max_connections': 20,
    'socket_keepalive': True,
    'socket_keepalive_options': {
        1: 1,  # TCP_KEEPIDLE
        2: 3,  # TCP_KEEPINTVL  
        3: 5,  # TCP_KEEPCNT
    },
    'retry_on_timeout': True,
    'health_check_interval': 30,
}

# Channel Layers Configuration with Redis Authentication
