from rest_framework import serializers
from .models import Review

class ReviewResponseSerializer(serializers.Serializer):
    """Serializer for review responses."""
    response_text = serializers.CharField(max_length=1000)

class ReviewSerializer(serializers.ModelSerializer):
    """Serializer for Review model."""
    
    responses = serializers.JSONField(read_only=True)
    
    class Meta:
        model = Review
        fields = [
            'id',
            'external_review_id',
            'property',
            'booking',
            'public_review',
            'private_feedback',
            'ratings',
            'reviewer_id',
            'reviewer_role',
            'reviewee_id',
            'reviewee_role',
            'channel_id',
            'thread_id',
            'is_hidden',
            'is_read',
            'submitted_at',
            'expires_at',
            'created_at',
            'updated_at',
            'responses',
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
