# Generated by Django 5.1.2 on 2025-06-28 07:58

import django_countries.fields
import services.storage.storage
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BillingAddress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('street_number', models.CharField(max_length=255)),
                ('postcode', models.CharField(max_length=10)),
                ('city', models.CharField(max_length=100)),
                ('country', django_countries.fields.CountryField(max_length=2)),
            ],
            options={
                'verbose_name_plural': 'Billing addresses',
            },
        ),
        migrations.CreateModel(
            name='BillingProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.Char<PERSON><PERSON>(max_length=255)),
                ('last_name', models.Char<PERSON>ield(max_length=255)),
                ('date_of_birth', models.DateField()),
                ('nationality', django_countries.fields.CountryField(max_length=2)),
                ('gender', models.CharField(max_length=10)),
                ('recipient_type', models.CharField(choices=[('NP', 'Natural Person'), ('CO', 'Company')], default='NP', max_length=2)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('iban', models.CharField(max_length=34)),
                ('id_document', models.FileField(blank=True, null=True, storage=services.storage.storage.DocumentStorage(bucket_name='heibooky', custom_domain='cdn.heibooky.com', default_acl='public-read', location='media', object_parameters={'CacheControl': 'max-age=86400', 'ContentDisposition': 'attachment', 'ContentType': 'application/pdf', 'Metadata': {'uploaded-by': 'heibooky-backend'}}), upload_to='uploads/id_documents/')),
                ('company_document', models.FileField(blank=True, null=True, storage=services.storage.storage.DocumentStorage(bucket_name='heibooky', custom_domain='cdn.heibooky.com', default_acl='public-read', location='media', object_parameters={'CacheControl': 'max-age=86400', 'ContentDisposition': 'attachment', 'ContentType': 'application/pdf', 'Metadata': {'uploaded-by': 'heibooky-backend'}}), upload_to='uploads/company_documents/')),
            ],
        ),
        migrations.CreateModel(
            name='Taxation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('has_vat_number', models.BooleanField(default=False)),
                ('vat_number', models.CharField(blank=True, max_length=20, null=True)),
                ('tin_number', models.CharField(blank=True, max_length=20, null=True)),
                ('tin_country', django_countries.fields.CountryField(blank=True, max_length=2, null=True)),
                ('rent_more_than_4_properties', models.BooleanField(default=False)),
            ],
        ),
    ]
