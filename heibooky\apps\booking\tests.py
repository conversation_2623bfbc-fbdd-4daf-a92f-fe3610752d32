from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.users.models import User
from apps.stay.models import Property, StaffRole, PropertyPermission
from apps.booking.models import Booking, Reservation, Customer
from apps.booking.utils import get_whatsapp_link
from apps.booking.serializers import BookingSerializer, CustomerSerializer
from django.utils import timezone
import uuid


class BookingAPITestCase(TestCase):
    def setUp(self):
        # Create test users
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            is_staff=True
        )

        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )

        self.cleaning_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )

        # Create test property
        self.property = Property.objects.create(
            name='Test Property',
            description='Test Description'
        )

        # Add users to property staff
        self.property.staffs.add(self.admin_user)
        self.property.staffs.add(self.regular_user)
        self.property.staffs.add(self.cleaning_user)

        # Create cleaning staff permission
        self.cleaning_permission = PropertyPermission.objects.create(
            name='cleaning_staff',
            description='Cleaning staff permission'
        )

        # Assign cleaning permission to cleaning user
        StaffRole.objects.create(
            property=self.property,
            user=self.cleaning_user,
            is_active=True
        ).permissions.add(self.cleaning_permission)

        # Create test customer
        self.customer = Customer.objects.create(
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>',
            telephone='1234567890'
        )

        # Create test reservation
        self.reservation = Reservation.objects.create(
            id=str(uuid.uuid4()),
            guest_name='Test Customer',
            checkin_date=timezone.now(),
            checkout_date=timezone.now() + timezone.timedelta(days=3),
            total_price=100.00,
            gross_price=120.00,
            total_tax=20.00,
            deposit=50.00,
            payment_due=50.00,
            commission_amount=10.00,
            addons={'breakfast': {'price': 15.00}}
        )

        # Create test booking
        self.booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.reservation,
            checkin_date=timezone.now().date(),
            checkout_date=(timezone.now() + timezone.timedelta(days=3)).date(),
            status='new',
            is_manual=True
        )

        # Set up API client
        self.client = APIClient()

    def test_admin_sees_financial_data(self):
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get(reverse('property-bookings-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that financial data is visible
        results = response.data.get('results', [])
        if results:
            booking_data = results[0]
            reservation_data = booking_data.get('reservation_data', {})
            self.assertNotEqual(reservation_data.get('total_price'), 0)
            self.assertNotEqual(reservation_data.get('gross_price'), 0)
            self.assertTrue(isinstance(reservation_data.get('addons'), dict))
            self.assertTrue(len(reservation_data.get('addons')) > 0)

    def test_cleaning_staff_cannot_see_financial_data(self):
        self.client.force_authenticate(user=self.cleaning_user)
        response = self.client.get(reverse('property-bookings-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that financial data is hidden
        results = response.data.get('results', [])
        if results:
            booking_data = results[0]
            self.assertTrue(booking_data.get('restricted_view', False))

            reservation_data = booking_data.get('reservation_data', {})
            self.assertEqual(reservation_data.get('total_price'), 0)
            self.assertEqual(reservation_data.get('gross_price'), 0)
            self.assertEqual(reservation_data.get('total_tax'), 0)
            self.assertEqual(reservation_data.get('deposit'), 0)
            self.assertEqual(reservation_data.get('payment_due'), 0)
            self.assertEqual(reservation_data.get('commission_amount'), 0)
            self.assertEqual(reservation_data.get('addons'), {})
            self.assertEqual(reservation_data.get('extra_fees'), {})
            self.assertEqual(reservation_data.get('taxes'), {})


class WhatsAppLinkTestCase(TestCase):
    """Test cases for WhatsApp link generation functionality."""

    def test_italian_phone_number(self):
        """Test Italian phone number generates Italian message."""
        phone = "+39 ************"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/391234567890"))
        self.assertIn("Ciao%2C%20grazie%20per%20la%20prenotazione", link)

    def test_french_phone_number(self):
        """Test French phone number generates French message."""
        phone = "+33 1 23 45 67 89"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/33123456789"))
        self.assertIn("Bonjour%2C%20merci%20pour%20votre", link)

    def test_german_phone_number(self):
        """Test German phone number generates German message."""
        phone = "+49 30 12345678"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/493012345678"))
        self.assertIn("Hallo%2C%20vielen%20Dank", link)

    def test_uk_phone_number(self):
        """Test UK phone number generates English message."""
        phone = "+44 20 7946 0958"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/442079460958"))
        self.assertIn("Hello%2C%20thank%20you%20for%20booking", link)

    def test_spanish_phone_number(self):
        """Test Spanish phone number generates Spanish message."""
        phone = "+34 91 123 4567"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/34911234567"))
        self.assertIn("Hola%2C%20gracias%20por%20reservar", link)

    def test_us_phone_number(self):
        """Test US phone number generates English message."""
        phone = "****** 123 4567"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/15551234567"))
        self.assertIn("Hello%2C%20thank%20you%20for%20booking", link)

    def test_international_format_with_00(self):
        """Test phone number starting with 00 (international format)."""
        phone = "0039 ************"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/391234567890"))
        self.assertIn("Ciao%2C%20grazie%20per%20la%20prenotazione", link)

    def test_unknown_country_defaults_to_english(self):
        """Test unknown country code defaults to English."""
        phone = "+999 ************"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/999123456789"))
        self.assertIn("Hello%2C%20thank%20you%20for%20booking", link)

    def test_empty_phone_number(self):
        """Test empty phone number returns empty string."""
        self.assertEqual(get_whatsapp_link(""), "")
        self.assertEqual(get_whatsapp_link(None), "")

    def test_invalid_phone_number(self):
        """Test invalid phone number returns empty string."""
        self.assertEqual(get_whatsapp_link("abc def"), "")
        self.assertEqual(get_whatsapp_link("!@#$%"), "")

    def test_phone_with_spaces_and_symbols(self):
        """Test phone number with various formatting is cleaned properly."""
        phone = "+39 (*************"
        link = get_whatsapp_link(phone)

        self.assertTrue(link.startswith("https://wa.me/391234567890"))


class BookingSerializerWhatsAppTestCase(TestCase):
    """Test WhatsApp link in BookingSerializer."""

    def setUp(self):
        self.customer = Customer.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            telephone="+39 ************"
        )

    def test_customer_serializer_includes_whatsapp_link(self):
        """Test CustomerSerializer includes WhatsApp link."""
        serializer = CustomerSerializer(self.customer)
        data = serializer.data

        self.assertIn('whatsapp_link', data)
        self.assertTrue(data['whatsapp_link'].startswith("https://wa.me/391234567890"))
        self.assertIn("Ciao%2C%20grazie%20per%20la%20prenotazione", data['whatsapp_link'])

    def test_customer_serializer_empty_phone(self):
        """Test CustomerSerializer with empty phone returns empty WhatsApp link."""
        self.customer.telephone = ""
        self.customer.save()

        serializer = CustomerSerializer(self.customer)
        data = serializer.data

        self.assertIn('whatsapp_link', data)
        self.assertEqual(data['whatsapp_link'], "")
