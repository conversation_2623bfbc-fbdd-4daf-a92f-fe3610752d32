from decimal import Decimal
import logging
import string
import time
import random
from typing import Dict, List, Any, Union, Optional
from dataclasses import dataclass
from datetime import datetime
from functools import wraps
from django.core.cache import cache
from django.utils.crypto import get_random_string
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from assets.translations import TRANSLATIONS_IT

# Configure logger
logger = logging.getLogger(__name__)

# Constants
VERIFICATION_CODE_TIMEOUT = 600  # 10 minutes in seconds
DEFAULT_LOGO_URL = "https://heibookybucket.fra1.cdn.digitaloceanspaces.com/heibookybucket/media/heibooky_logo.jpg"

class EmailSendError(Exception):
    """Custom exception for email sending errors"""
    pass

@dataclass
class EmailRecipient:
    """Data class for email recipient information"""
    email: str
    name: Optional[str] = None

@dataclass
class EmailContent:
    """Data class for email content"""
    subject: str
    template_name: str
    context: Dict[str, Any]

class EmailService:
    """Base class for email services"""
    MAX_RETRIES = 3
    BASE_DELAY = 1  # Initial delay in seconds
    MAX_DELAY = 10  # Maximum delay in seconds
    JITTER = 0.1   # Random jitter factor

    def __init__(self, from_email: Optional[str] = None, language: str = 'it'):
        self.from_email = from_email or f"Heibooky <{settings.DEFAULT_FROM_EMAIL}>"
        self.language = language
        self.translations = TRANSLATIONS_IT

    def _prepare_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare common context for all emails"""
        base_context = {
            'site_name': 'Heibooky',
            'logo_url': DEFAULT_LOGO_URL,
            'translations': self.translations,
        }
        return {**base_context, **context}

    def _exponential_backoff(self, attempt: int) -> float:
        """Calculate delay with exponential backoff and jitter"""
        delay = min(self.BASE_DELAY * (2 ** attempt), self.MAX_DELAY)
        jitter = random.uniform(-self.JITTER * delay, self.JITTER * delay)
        return delay + jitter

    def _retry_with_backoff(self, func):
        """Decorator for retrying operations with exponential backoff"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(self.MAX_RETRIES):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < self.MAX_RETRIES - 1:
                        delay = self._exponential_backoff(attempt)
                        logger.warning(
                            f"Email send attempt {attempt + 1} failed: {str(e)}. "
                            f"Retrying in {delay:.2f} seconds..."
                        )
                        time.sleep(delay)

            # If we get here, all retries failed
            logger.error(
                f"Failed to send email after {self.MAX_RETRIES} attempts: {str(last_exception)}"
            )
            raise EmailSendError(f"Failed after {self.MAX_RETRIES} attempts") from last_exception

        return wrapper

    def send_email(self, recipients: Union[EmailRecipient, List[EmailRecipient]], content: EmailContent, attachments: Optional[List] = None) -> bool:
        """
        Generic email sending function with robust error handling and retry logic

        Args:
            recipients: Single recipient or list of recipients
            content: EmailContent object containing subject, template and context
            attachments: Optional list of MessageAttachment objects
        """
        @self._retry_with_backoff
        def _send_with_retry():
            # Convert single recipient to list
            recipient_list = recipients if isinstance(recipients, list) else [recipients]
            email_list = [r.email for r in recipient_list]

            try:
                context = self._prepare_context(content.context)
                msg = EmailMultiAlternatives(
                    subject=content.subject,
                    body=render_to_string(f'emails/{content.template_name}.txt', context),
                    from_email=self.from_email,
                    to=email_list
                )

                html_content = render_to_string(f'emails/{content.template_name}.html', context)
                msg.attach_alternative(html_content, "text/html")

                if attachments:
                    for attachment in attachments:
                        try:
                            file_content = attachment.file.read()
                            msg.attach(
                                attachment.file_name,
                                file_content,
                                attachment.content_type
                            )
                            attachment.file.seek(0)
                        except Exception as e:
                            logger.error(f"Failed to attach file {attachment.file_name}: {str(e)}")

                msg.send(fail_silently=False)
                logger.info(f"Email sent successfully to {', '.join(email_list)}. Subject: {content.subject}")
                return True

            except Exception as e:
                error_msg = f"Failed to send email to {', '.join(email_list)}: {str(e)}"
                logger.error(error_msg)
                raise EmailSendError(error_msg) from e

        return _send_with_retry()

    def send_insufficient_funds_notification(self, required_amount: Decimal, available_amount: Decimal, affected_bookings: list) -> bool:
        """
        Send notification about insufficient funds to process payouts.

        Args:
            required_amount: Total amount needed for payouts
            available_amount: Current available balance
            affected_bookings: List of bookings affected by insufficient funds
        """
        recipient = EmailRecipient(email='<EMAIL>')
        content = EmailContent(
            subject='Fondi insufficienti per elaborare i pagamenti',
            template_name='insufficient_funds',
            context={
                'required_amount': required_amount,
                'available_amount': available_amount,
                'missing_amount': required_amount - available_amount,
                'affected_bookings': affected_bookings,
                'stripe_dashboard_url': 'https://dashboard.stripe.com/balance',
            }
        )
        return self.send_email(recipient, content)

    def send_support_notification(self, support_message) -> bool:
        """
        Send notification about new support message to admin.

        Args:
            support_message: SupportMessage instance containing user message details
        """
        try:
            recipient = EmailRecipient(email='<EMAIL>')

            # Format attachments for template context
            attachment_info = []
            attachments = support_message.attachments.all()
            chat= support_message.chat
            if attachments:
                attachment_info = [{
                    'file_name': att.file_name,
                    'file_size': att.file_size
                } for att in attachments]

            content = EmailContent(
                subject=f"{self.translations['new_support_request']}: {chat.user.email}",
                template_name='support_mail',
                context={
                    'user_name': chat.user.name,
                    'user_email': chat.user.email,
                    'priority': chat.get_priority_display(),
                    'message': support_message.message,
                    'attachments': attachment_info,
                    'admin_url': f"{settings.SUPPORT_URL}/chat/{chat.id}/",
                }
            )

            # Send email with attachments
            success = self.send_email(recipient, content, attachments=attachments)

            if success:
                logger.info(f"Support notification sent with {len(attachments)} attachments for message ID: {support_message.id}")
            return success

        except Exception as e:
            error_msg = f"Failed to send support notification: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False

    def send_admin_message_notification(self, support_message) -> bool:
        """
        Send notification to user when admin sends a message and it remains unread after 5 minutes.

        Args:
            support_message: SupportMessage instance from admin
        """
        try:
            recipient = EmailRecipient(email=support_message.chat.user.email, name=support_message.chat.user.name)

            chat_url = f"{settings.FRONTEND_URL}/dashboard/chat"

            content = EmailContent(
                subject=f"{self.translations['support_response_received']}",
                template_name='admin_message_notification',
                context={
                    'user_name': support_message.chat.user.name,
                    'message': support_message.message,
                    'chat_url': chat_url,
                    'priority': support_message.chat.get_priority_display(),
                }
            )

            success = self.send_email(recipient, content)

            if success:
                logger.info(f"Admin message notification sent to user {support_message.chat.user.email} for message ID: {support_message.id}")
            return success

        except Exception as e:
            error_msg = f"Failed to send admin message notification: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False

    def send_user_message_notification(self, support_message) -> bool:
        """
        Send instant notification to admin when user sends a message.

        Args:
            support_message: SupportMessage instance from user
        """
        try:
            recipient = EmailRecipient(email='<EMAIL>')

            # Format attachments for template context
            attachment_info = []
            attachments = support_message.attachments.all()
            if attachments:
                attachment_info = [{
                    'file_name': att.file_name,
                    'file_size': att.file_size
                } for att in attachments]

            content = EmailContent(
                subject=f"{self.translations['new_support_message']}: {support_message.chat.user.email}",
                template_name='user_message_notification',
                context={
                    'user_name': support_message.chat.user.name,
                    'user_email': support_message.chat.user.email,
                    'message': support_message.message,
                    'priority': support_message.chat.get_priority_display(),
                    'attachments': attachment_info,
                    'admin_url': f"{settings.ADMIN_URL}/support/supportmessage/{support_message.id}/",
                }
            )

            # Send email with attachments
            success = self.send_email(recipient, content, attachments=attachments)

            if success:
                logger.info(f"User message notification sent for message ID: {support_message.id}")
            return success

        except Exception as e:
            error_msg = f"Failed to send user message notification: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False

    def send_payout_email(self, payout, invoice) -> bool:
        """
        Send payout notification email to user and admin.

        Args:
            payout: Payout instance containing payment details
            invoice: Invoice instance containing user and invoice details
        """
        try:
            # Send to user
            user_recipient = EmailRecipient(email=payout.customer.user.email)
            admin_recipient = EmailRecipient(email='<EMAIL>')

            content = EmailContent(
                subject=f"{self.translations['payout_notification']}",
                template_name='payout_email',
                context={
                    'payout': payout,
                    'invoice': invoice,
                    'dashboard_url': f"{settings.FRONTEND_URL}/dashboard/settings/invoices/",
                }
            )

            # Send to both user and admin
            success = self.send_email([user_recipient, admin_recipient], content)

            if success:
                logger.info(f"Payout notification sent for payout ID: {payout.id}")
            return success

        except Exception as e:
            error_msg = f"Failed to send payout notification: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False

    def send_booking_cancellation_request(self, user, booking, reason) -> bool:
        """
        Send booking cancellation request notification to admin.

        Args:
            user: User requesting the cancellation
            booking: Booking instance to be cancelled
            reason: Reason for cancellation request
        """
        try:
            recipient = EmailRecipient(email='<EMAIL>')

            # Format dates for display
            checkin_date = booking.checkin_date.strftime('%d/%m/%Y')
            checkout_date = booking.checkout_date.strftime('%d/%m/%Y')

            # Get guest name from reservation data
            guest_name = booking.reservation_data.guest_name

            # Create admin URL for the booking
            admin_url = f"{settings.ADMIN_URL}/booking/reservation/{booking.reservation_data.id}/"

            content = EmailContent(
                subject=f"Richiesta di cancellazione prenotazione: {booking.property.name}",
                template_name='booking_cancellation_request',
                context={
                    'user_name': user.name,
                    'user_email': user.email,
                    'property_name': booking.property.name,
                    'booking_id': str(booking.id),
                    'checkin_date': checkin_date,
                    'checkout_date': checkout_date,
                    'guest_name': guest_name,
                    'reason': reason,
                    'admin_url': admin_url,
                }
            )

            success = self.send_email(recipient, content)

            if success:
                logger.info(f"Booking cancellation request sent for booking ID: {booking.id}")
            return success

        except Exception as e:
            error_msg = f"Failed to send booking cancellation request: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False

    def send_overbooking_alert(self, property_obj, existing_booking, new_booking) -> bool:
        """
        Send overbooking alert notification to admin.

        Args:
            property_obj: Property instance where overbooking occurred
            existing_booking: The existing booking that conflicts
            new_booking: The new booking that causes the conflict
        """
        try:
            recipient = EmailRecipient(email='<EMAIL>')

            # Calculate overlap period
            overlap_start = max(existing_booking.checkin_date, new_booking.checkin_date)
            overlap_end = min(existing_booking.checkout_date, new_booking.checkout_date)

            # Create admin URL for viewing bookings
            admin_url = f"{settings.ADMIN_URL}/booking/booking/?property__id__exact={property_obj.id}"

            content = EmailContent(
                subject=f"🚨 Overbooking Rilevato - {property_obj.name}",
                template_name='overbooking_alert',
                context={
                    'property': property_obj,
                    'existing_booking': existing_booking,
                    'new_booking': new_booking,
                    'overlap_start': overlap_start,
                    'overlap_end': overlap_end,
                    'admin_url': admin_url,
                    'current_year': datetime.now().year,
                }
            )

            success = self.send_email(recipient, content)

            if success:
                logger.info(
                    f"Overbooking alert sent for property {property_obj.name}. "
                    f"Conflicting bookings: {existing_booking.id} and {new_booking.id}"
                )
            return success

        except Exception as e:
            error_msg = f"Failed to send overbooking alert: {str(e)}"
            logger.error(
                error_msg, 
                exc_info=True,
                extra={
                    'property_id': property_obj.id,
                    'existing_booking_id': str(existing_booking.id),
                    'new_booking_id': str(new_booking.id)
                }
            )
            return False

class VerificationService:
    """Service for handling verification codes"""
    @staticmethod
    def generate_code(length: int = 4) -> str:
        """Generate a numerical verification code"""
        return get_random_string(length=length, allowed_chars=string.digits)

    @staticmethod
    def store_code(email: str, code: str) -> None:
        """Store verification code in cache"""
        cache.set(f'verification_code_{email}', code, timeout=VERIFICATION_CODE_TIMEOUT)

    @staticmethod
    def verify_code(email: str, code: str) -> bool:
        """Verify stored code against provided code"""
        stored_code = cache.get(f'verification_code_{email}')
        return stored_code == code

class ReservationEmailService(EmailService):
    """Service for handling reservation-related emails"""
    def __init__(self, property_obj, customer, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.property = property_obj
        self.customer = customer

    def _get_staff_members(self) -> List[EmailRecipient]:
        """Get list of staff members as email recipients"""
        return [
            EmailRecipient(email=staff.email, name=staff.name)
            for staff in self.property.staffs.all()
        ]

    def _send_to_all_staff(self, content: EmailContent) -> None:
        """Send email to all staff members in a single message"""
        staff_members = self._get_staff_members()
        if not staff_members:
            logger.warning(f"No staff members found for property {self.property.id}")
            return

        max_retries = 3
        retry_delay = 1  # seconds
        attempt = 0

        while attempt < max_retries:
            try:
                reservation_recipient = EmailRecipient(email="<EMAIL>", name="Heibooky Reservations")
                all_recipients = staff_members + [reservation_recipient]
                self.send_email(all_recipients, content)
                return  # Success - exit the loop
            except EmailSendError as e:
                attempt += 1
                if attempt == max_retries:
                    logger.error(f"Failed to send email to staff members after {max_retries} attempts: {str(e)}")
                else:
                    logger.warning(f"Email send attempt {attempt} failed: {str(e)}. Retrying...")
                    time.sleep(retry_delay)


    def notify_new_reservation(self, reservation):
        """Notify staff about new reservation"""
        content = EmailContent(
            subject=f"{self.translations['new_reservation_notification']}: {self.property.name}",
            template_name='new_reservation',
            context={
                'reservation': reservation,
                'customer': self.customer,
            }
        )
        self._send_to_all_staff(content)

    def notify_modification(self, reservation, changes):
        """Notify staff about reservation modification"""
        content = EmailContent(
            subject=f"{self.translations['reservation_modified_notification']}: {self.property.name}",
            template_name='modified_reservation',
            context={
                'reservation': reservation,
                'customer': self.customer,
                'changes': changes,
            }
        )
        self._send_to_all_staff(content)

    def notify_cancellation(self, reservation):
        """Notify staff about reservation cancellation"""
        refund_amount = reservation.deposit if reservation.deposit else 0
        content = EmailContent(
            subject=f"{self.translations['reservation_cancelled_notification']}: {self.property.name}",
            template_name='cancelled_reservation',
            context={
                'reservation': reservation,
                'customer': self.customer,
                'cancelled_date': reservation.modified_at or datetime.now(),
                'refund_amount': refund_amount,
            }
        )
        self._send_to_all_staff(content)

    def send_reservation_reminder(self, reservation, is_first_reminder=True):
        """
        Send reservation reminder email to staff members.

        Args:
            reservation: Reservation instance with booking details
            is_first_reminder: True if this is the 7-day reminder, False if it's the check-in day reminder
        """
        if is_first_reminder:
            reminder_title = f"Promemoria: Arrivo ospite tra 7 giorni"
            reminder_subtitle = f"Un ospite arriverà presso {self.property.name} tra 7 giorni. Ecco i dettagli della prenotazione."
            action_message = (f"Si prega di contattare l'ospite al numero {self.customer.telephone} "
                             f"per confermare l'orario di arrivo e fornire le istruzioni per il check-in.")
        else:
            reminder_title = f"Promemoria: Arrivo ospite OGGI"
            reminder_subtitle = f"Un ospite arriverà presso {self.property.name} oggi. Ecco i dettagli della prenotazione."
            action_message = (f"Si prega di contattare l'ospite al numero {self.customer.telephone} "
                             f"per confermare l'orario di arrivo e assicurarsi che tutto sia pronto per il check-in.")

        content = EmailContent(
            subject=reminder_title,
            template_name='reservation_reminder',
            context={
                'reminder_title': reminder_title,
                'reminder_subtitle': reminder_subtitle,
                'action_message': action_message,
                'reservation': reservation,
                'customer': self.customer,
            }
        )

        self._send_to_all_staff(content)

class AccountEmailService(EmailService):
    """Service for handling account-related emails"""
    def send_verification_email(self, email: str, code: str) -> bool:
        """Send account verification email"""
        recipient = EmailRecipient(email=email)
        content = EmailContent(
            subject='Verifica la tua mail',
            template_name='account_verification',
            context={
                'verification_code': code,
                'valid_minutes': VERIFICATION_CODE_TIMEOUT // 60,
            }
        )
        return self.send_email(recipient, content)

    def send_password_reset_email(self, email: str, code: str) -> bool:
        """Send password reset email"""
        recipient = EmailRecipient(email=email)
        content = EmailContent(
            subject='Richiesta di reimpostazione della password',
            template_name='password_reset',
            context={
                'reset_code': code,
                'valid_minutes': 30,
            }
        )
        return self.send_email(recipient, content)

    def send_account_deletion_email(self, email: str, name: Optional[str] = None) -> bool:
        """
        Send account deletion notification email.
        """
        recipient = EmailRecipient(email=email, name=name)
        content = EmailContent(
            subject='Account Eliminato',
            template_name='account_deletion',
            context={
                'user_name': name
            }
        )
        return self.send_email(recipient, content)

    def send_welcome_email(self, user) -> bool:
        """Send welcome email to new user"""
        recipient = EmailRecipient(email=user.email, name=user.name)
        content = EmailContent(
            subject=self.translations['welcome_subject'],
            template_name='welcome_mail',
            context={
                'user': user,
            }
        )
        return self.send_email(recipient, content)

    def send_password_changed_email(self, email: str, context: dict) -> bool:
        """Send password change notification email"""
        recipient = EmailRecipient(email=email)
        content = EmailContent(
            subject='Notifica di modifica password',
            template_name='change_password',
            context=context
        )
        return self.send_email(recipient, content)

    def send_new_login_location_email(self, email: str, context: dict) -> bool:
        """
        Send notification about login from new location.

        Args:
            email: User's email address
            context: Dictionary containing login details:
                - login_time: Timestamp of the login attempt
                - location: Current login location
                - device: Device used for login
                - previous_location: Previous login location
                - ip_address: IP address of the login attempt
        """
        recipient = EmailRecipient(email=email)

        # Add security URL to context
        context['account_security_url'] = f"{settings.FRONTEND_URL}/dashboard/settings/account/"

        content = EmailContent(
            subject=f"{self.translations['new_login_detected']}: {context['location']}",
            template_name='new_login_location',
            context=context
        )

        try:
            success = self.send_email(recipient, content)
            if success:
                logger.info(f"Login location alert sent to {email} for location: {context['location']}")
            return success
        except EmailSendError as e:
            logger.error(f"Failed to send login location alert to {email}: {str(e)}")
            return False

class PropertyEmailService(EmailService):
    """Service for handling property-related emails"""
    def send_deletion_request(self, user, property_obj, admin_url: str) -> bool:
        """Send property deletion request to admin"""
        recipient = EmailRecipient(email='<EMAIL>')
        content = EmailContent(
            subject=f'Richiesta eliminazione proprietà: {property_obj.name}',
            template_name='property_delete',
            context={
                'user_name': user.name,
                'user_email': user.email,
                'property_id': property_obj.hotel_id,
                'property_name': property_obj.name,
                'admin_url': admin_url,
            }
        )
        return self.send_email(recipient, content)

    def send_setup_reminder(self, user, property_obj, missing_items: list) -> bool:
        """
        Send reminder email for incomplete property setup.

        Args:
            user: User who owns the property
            property_obj: Property instance with incomplete setup
            missing_items: List of setup items that need to be completed
        """
        try:
            recipient = EmailRecipient(email=user.email, name=user.name)

            # Determine reminder number for subject line
            reminder_count = property_obj.metadata.reminder_count + 1
            reminder_text = "Primo promemoria" if reminder_count == 1 else \
                           "Secondo promemoria" if reminder_count == 2 else \
                           "Promemoria finale"

            dashboard_url = f"{settings.FRONTEND_URL}/dashboard/properties/{property_obj.id}/settings"

            content = EmailContent(
                subject=f"{reminder_text}: Completa la configurazione di {property_obj.name}",
                template_name='property_setup_reminder',
                context={
                    'user': user,
                    'property': property_obj,
                    'missing_items': missing_items,
                    'dashboard_url': dashboard_url,
                    'reminder_number': reminder_count
                }
            )

            success = self.send_email(recipient, content)

            if success:
                logger.info(f"Setup reminder #{reminder_count} sent for property {property_obj.id} to {user.email}")
            return success

        except Exception as e:
            error_msg = f"Failed to send property setup reminder: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False

    def send_cancelation_policy_request(self, user, property_obj, cancelation_policy_type) -> bool:
        """Send property cancelation policy setup request to admin"""
        recipient = EmailRecipient(email='<EMAIL>')
        content = EmailContent(
            subject=f"{self.translations['cancelation_policy_set']}: {property_obj.name}",
            template_name='cancelation_policy',
            context={
                'user_name': user.name,
                'user_email': user.email,
                'property': property_obj,
                'cancelation_policy': cancelation_policy_type,
                'admin_url':'https://partner.su-api.com/app/c2FtZXNvbHV0aW9uc3JsLnN1aXNzdS5jb20=/',
            }
        )
        return self.send_email(recipient, content)

    def send_deletion_confirmation(self, property_obj, user) -> bool:
        """Send property deletion confirmation to user"""
        recipient = EmailRecipient(email=user.email, name=user.name)
        content = EmailContent(
            subject=f"{self.translations['property_deletion_confirmed']}: {property_obj.name}",
            template_name='property_delete_confirm',
            context={
                'user_name': user.name,
                'property_id': property_obj.hotel_id,
                'property_name': property_obj.name,
            }
        )
        return self.send_email(recipient, content)

    def send_onboarding_confirmation(self, user, property_obj) -> bool:
        """Send property onboarding confirmation"""
        recipient = EmailRecipient(email=user.email, name=user.name)
        content = EmailContent(
            subject=f"{self.translations['property_onboarding_confirmed']}: {property_obj.name}",
            template_name='property_onboarded',
            context={
                'user': user,
                'property': property_obj,
            }
        )
        return self.send_email(recipient, content)

    def send_property_invitation(self, invite_obj) -> bool:
        """Send property invitation email"""
        recipient = EmailRecipient(email=invite_obj.email)
        frontend_url = settings.FRONTEND_URL
        accept_url = f"{frontend_url}/invites/{invite_obj.id}"
        expiration_hours = 72

        # Pre-format the translation strings
        invitation_message = self.translations['invitation_message'].format(
            invite_obj.invited_by.email,
            invite_obj.property.name
        )
        invitation_expires_notice = self.translations['invitation_expires_notice'].format(
            expiration_hours
        )

        content = EmailContent(
            subject=f'Invito a gestire una proprietà su {self.translations.get("site_name", "Heibooky")}',
            template_name='user_invite',
            context={
                'inviter_email': invite_obj.invited_by.email,
                'property_name': invite_obj.property.name,
                'accept_url': accept_url,
                'expiration_hours': expiration_hours,
                'invitation_message': invitation_message,
                'invitation_expires_notice': invitation_expires_notice,
            }
        )
        return self.send_email(recipient, content)

    def send_location_change_notification(self, location, onboarded_properties, user=None) -> bool:
        """
        Send notification about location changes to admin.

        Args:
            location: Location instance that was changed
            onboarded_properties: QuerySet of onboarded properties associated with this location
            user: User who made the change (optional)
        """
        try:
            recipient = EmailRecipient(email='<EMAIL>')

            # Format property information for template
            property_info = [{
                'name': prop.name,
                'hotel_id': prop.hotel_id,
                'chain_id': prop.chain_id
            } for prop in onboarded_properties]

            content = EmailContent(
                subject=f"{self.translations['location_change_notification']}: {location.city}",
                template_name='location_change',
                context={
                    'location': location,
                    'properties': property_info,
                    'user': user,
                    'changed_at': datetime.now(),
                }
            )

            success = self.send_email(recipient, content)

            if success:
                logger.info(f"Location change notification sent for location ID: {location.id}")
            return success

        except Exception as e:
            error_msg = f"Failed to send location change notification: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False