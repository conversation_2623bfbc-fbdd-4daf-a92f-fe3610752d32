import logging
from typing import Optional, Dict, Any
from django.conf import settings
from services.storage.storage import CDNStorageService
from services.storage.tasks import optimize_and_upload_image, optimize_and_upload_image_from_file, batch_optimize_images
from services.storage.exceptions import StorageServiceUnavailable

logger = logging.getLogger(__name__)


class CDNUtils:
    """
    Utility class for CDN operations with fallback handling
    """
    
    @staticmethod
    def get_storage(storage_type: str = 'default'):
        """
        Get storage instance with error handling
        """
        try:
            return CDNStorageService.get_storage_backend(storage_type)
        except Exception as e:
            logger.error(f"Failed to get storage backend {storage_type}: {str(e)}")
            raise StorageServiceUnavailable(f"Storage service unavailable: {str(e)}")
    
    @staticmethod
    def upload_file_sync(file_content, filename: str, storage_type: str = 'default') -> Optional[str]:
        """
        Synchronously upload file to CDN
        
        Args:
            file_content: File content to upload
            filename: Name of the file
            storage_type: Type of storage
            
        Returns:
            CDN URL or None if failed
        """
        try:
            return CDNStorageService.upload_file(file_content, filename, storage_type)
        except Exception as e:
            logger.error(f"Sync upload failed for {filename}: {str(e)}")
            return None
    
    @staticmethod
    def upload_image_async(image_data: bytes, filename: str, model_name: str, object_id: str, field_name: str = 'image') -> str:
        """
        Asynchronously upload and optimize image
        
        Args:
            image_data: Raw image data
            filename: Original filename
            model_name: Django model name
            object_id: Model instance ID
            field_name: Field name to update
            
        Returns:
            Task ID for monitoring
        """
        try:
            task = optimize_and_upload_image.delay(
                image_data, filename, model_name, object_id, field_name
            )
            logger.info(f"Queued async image upload: {filename} (task: {task.id})")
            return task.id
        except Exception as e:
            logger.error(f"Failed to queue async image upload for {filename}: {str(e)}")
            raise

    @staticmethod
    def upload_image_async_from_file(file_obj, filename: str, model_name: str, object_id: str, field_name: str = 'image') -> str:
        """
        Asynchronously upload and optimize image from file object without loading into memory
        
        Args:
            file_obj: File object or uploaded file
            filename: Original filename
            model_name: Django model name
            object_id: Model instance ID
            field_name: Field name to update
            
        Returns:
            Task ID for monitoring
        """
        import tempfile
        import os
        
        try:
            # Create a temporary file to store the image data
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
                # Stream the file content to temporary file in chunks
                file_obj.seek(0)
                for chunk in file_obj.chunks() if hasattr(file_obj, 'chunks') else iter(lambda: file_obj.read(8192), b''):
                    temp_file.write(chunk)
                temp_file.flush()
                temp_file_path = temp_file.name
            
            # Queue the task with the temporary file path
            task = optimize_and_upload_image_from_file.delay(
                temp_file_path, filename, model_name, object_id, field_name
            )
            logger.info(f"Queued async image upload from file: {filename} (task: {task.id}, temp: {temp_file_path})")
            return task.id
            
        except Exception as e:
            # Clean up temp file if it was created
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except OSError:
                    pass
            logger.error(f"Failed to queue async image upload from file for {filename}: {str(e)}")
            raise
    
    @staticmethod
    def upload_images_batch_async(image_list: list) -> str:
        """
        Batch upload multiple images asynchronously
        
        Args:
            image_list: List of image information dictionaries
            
        Returns:
            Task ID for monitoring
        """
        try:
            task = batch_optimize_images.delay(image_list)
            logger.info(f"Queued batch image upload: {len(image_list)} images (task: {task.id})")
            return task.id
        except Exception as e:
            logger.error(f"Failed to queue batch image upload: {str(e)}")
            raise
    
    @staticmethod
    def get_cdn_url(filename: str, storage_type: str = 'default') -> Optional[str]:
        """
        Get CDN URL for a file with fallback
        
        Args:
            filename: Name of the file
            storage_type: Type of storage
            
        Returns:
            CDN URL or fallback URL
        """
        try:
            # Try to get CDN URL
            cdn_url = CDNStorageService.get_file_url(filename, storage_type)
            if cdn_url:
                return cdn_url
            
            # Fallback to direct construction if file exists
            storage = CDNUtils.get_storage(storage_type)
            if storage.exists(filename):
                return storage.url(filename)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get CDN URL for {filename}: {str(e)}")
            return None
    
    @staticmethod
    def delete_file(filename: str, storage_type: str = 'default') -> bool:
        """
        Delete file from CDN storage
        
        Args:
            filename: Name of the file
            storage_type: Type of storage
            
        Returns:
            True if successful
        """
        try:
            return CDNStorageService.delete_file(filename, storage_type)
        except Exception as e:
            logger.error(f"Failed to delete file {filename}: {str(e)}")
            return False
    
    @staticmethod
    def validate_file_for_upload(file_obj, allowed_types: list = None, max_size: int = None) -> Dict[str, Any]:
        """
        Validate file before upload
        
        Args:
            file_obj: File object to validate
            allowed_types: List of allowed MIME types
            max_size: Maximum file size in bytes
            
        Returns:
            Validation result dictionary
        """
        result = {
            'valid': True,
            'errors': [],
            'file_info': {}
        }
        
        try:
            # Get file info
            result['file_info'] = {
                'name': getattr(file_obj, 'name', 'unknown'),
                'size': getattr(file_obj, 'size', 0),
                'content_type': getattr(file_obj, 'content_type', 'unknown')
            }
            
            # Validate file size
            if max_size and result['file_info']['size'] > max_size:
                result['valid'] = False
                result['errors'].append(f"File size ({result['file_info']['size']} bytes) exceeds limit ({max_size} bytes)")
            
            # Validate content type
            if allowed_types and result['file_info']['content_type'] not in allowed_types:
                result['valid'] = False
                result['errors'].append(f"File type '{result['file_info']['content_type']}' not allowed")
            
            # Validate file exists and has content
            if result['file_info']['size'] == 0:
                result['valid'] = False
                result['errors'].append("File is empty")
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"Validation error: {str(e)}")
            logger.error(f"File validation error: {str(e)}")
        
        return result
    
    @staticmethod
    def check_cdn_availability() -> bool:
        """
        Quick check for CDN availability
        
        Returns:
            True if CDN is available
        """
        try:
            health_info = CDNStorageService.check_cdn_health()
            return health_info.get('cdn_available', False) and health_info.get('storage_available', False)
        except Exception as e:
            logger.error(f"CDN availability check failed: {str(e)}")
            return False
    
    @staticmethod
    def get_media_url(relative_path: str) -> str:
        """
        Get full media URL with CDN prefix
        
        Args:
            relative_path: Relative path to media file
            
        Returns:
            Full CDN URL
        """
        if not relative_path:
            return ''
        
        # Remove leading slash if present
        relative_path = relative_path.lstrip('/')
        
        # Construct CDN URL
        cdn_base = getattr(settings, 'AWS_CDN_URL', '')
        if cdn_base:
            media_location = getattr(settings, 'AWS_LOCATION', 'media')
            return f"{cdn_base}/{media_location}/{relative_path}"
        else:
            # Fallback to MEDIA_URL
            return f"{settings.MEDIA_URL}{relative_path}"


# Backwards compatibility
cdn_utils = CDNUtils()
