from rest_framework import serializers
from apps.integrations.models import Payout, Invoice, Notification, DownloadableTemplate


class InvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Invoice
        fields = ['id', 'payout', 'pdf_file', 'progressive_number', 'created_at']

class PayoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payout
        fields = [
            'id', 'customer', 'stripe_payment_intent_id', 'amount', 
            'currency', 'status', 'created_at', 'updated_at', 'invoice_status'
        ]

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'title', 'message', 'is_read', 'created_at']

class DownloadableTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadableTemplate
        fields = ['id', 'title', 'description', 'file', 'category', 'created_at']


class DashboardUnreadCountsSerializer(serializers.Serializer):
    """
    Serializer for dashboard unread counts response.

    Returns aggregated unread counts for reviews, notifications, and support messages.
    """
    unread_reviews_count = serializers.IntegerField(
        help_text="Number of unread reviews for user's properties"
    )
    unread_notifications_count = serializers.IntegerField(
        help_text="Number of unread notifications for the user"
    )
    unread_support_messages_count = serializers.IntegerField(
        help_text="Number of unread support messages for the user"
    )