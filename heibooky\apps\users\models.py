import uuid
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import PhoneNumberField
from django.core.validators import FileExtensionValidator
from django.core.exceptions import ValidationError
from django.core.files.storage import storages

def validate_image_size(file):
    limit = 5 * 1024 * 1024  # 5MB
    if file.size > limit:
        raise ValidationError('File size cannot exceed 5MB.')

class UserManager(BaseUserManager):
    """
    Custom manager for the User model, extending BaseUserManager.
    Handles the creation of regular users and superusers.
    """

    def create_user(self, email, name, phone=None, password=None, is_verified=False):
        """
        Creates and saves a regular user with the given email, name, phone, and password.
        """
        if not email:
            raise ValueError(_("The Email field is required"))

        # Normalize the email
        email = self.normalize_email(email)

        with transaction.atomic():
            # Create the user instance
            user = self.model(
                email=email,
                name=name,
                phone=phone,
                is_verified=is_verified,
            )
            # Set the user's password
            if password:
                user.set_password(password)
            else:
                user.set_unusable_password()  # If no password is provided, make it unusable

            user.save(using=self._db)

            # Automatically create the user profile after saving the user
            UserProfile.objects.create(user=user)
        return user

    def create_superuser(self, email, name, phone=None, password=None):
        """
        Creates and saves a superuser with the given email, name, phone, and password.
        """
        if not email:
            raise ValueError(_("The Email field is required"))
            
        with transaction.atomic():
            user = self.model(email=self.normalize_email(email), name=name, phone=phone, is_verified=True)
            user.set_password(password)
            user.is_admin = True
            user.is_staff = True
            user.is_superuser = True
            user.save(using=self._db)
            
            # Automatically create the user profile after saving the superuser
            UserProfile.objects.create(user=user)
            
        return user

class User(AbstractBaseUser, PermissionsMixin):
    """
    Custom User model that uses email as the primary identifier.
    Includes fields for email, name, phone, and several status indicators.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(
        verbose_name=_("Email Address"),
        max_length=255,
        unique=True,
        error_messages={
            'unique': _("A user with that email already exists."),
        },
    )
    name = models.CharField(max_length=255, verbose_name=_("Full Name"))
    phone = PhoneNumberField(
        null=True,
        blank=True,
        verbose_name=_("Phone Number"),
        help_text=_("Used as an alternative verification method"),
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_verified = models.BooleanField(
        default=False,
        verbose_name=_("Verified"),
        help_text=_("Designates whether this user has completed verification."),
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Designates whether this user should be treated as active."),
    )
    has_set_password = models.BooleanField(
        default=False,
        verbose_name=_("Password Set"),
        help_text=_("Indicates if the user has set a password.")
    )
    is_admin = models.BooleanField(default=False, verbose_name=_("Admin Status"))
    is_staff = models.BooleanField(
        default=False, 
        verbose_name=_("Staff Status"),
        help_text=_("Designates whether the user can log into this admin site.")
    )

    objects = UserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["name"]

    class Meta:
        verbose_name = _("User")
        verbose_name_plural = _("Users")
        ordering = ['-created_at']

    def __str__(self):
        """
        Return the user's name if available; otherwise, return their email address.
        """
        return str(self.name) if self.name.strip() else str(self.email)

    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='custom_user_permissions_set',  # Custom related_name
        blank=True,
        help_text=_('Specific permissions for this user.'),
        verbose_name=_('user permissions'),
    )

class UserProfile(models.Model):
    """
    UserProfile model to store additional information related to the user.
    Includes an image field for profile pictures.
    """
    ALLOWED_IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif']

    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="profile"
    )
    image = models.ImageField(
        upload_to="profile_images/",
        null=True,
        blank=True,
        verbose_name=_("Profile Image"),
        validators=[
            FileExtensionValidator(allowed_extensions=ALLOWED_IMAGE_EXTENSIONS),
            validate_image_size
        ],
        storage=storages['photo_storage']
    )
    last_login_location = models.JSONField(null=True, blank=True)
    has_billing_profile = models.BooleanField(default=False, verbose_name=_("Has Billing Profile"))
    is_customer = models.BooleanField(default=False, verbose_name=_("Is Customer"))

    class Meta:
        verbose_name = _("User Profile")
        verbose_name_plural = _("User Profiles")

    def __str__(self):
        return self.user.name
